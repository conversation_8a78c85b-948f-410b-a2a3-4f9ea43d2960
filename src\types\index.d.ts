import type { Request } from "express";

declare global {
  export type MNO = "AIRTEL" | "MTN" | "ZAMTEL" | "ZEDMOBILE";
  export interface Client {
    id: any;
    business_name: string;
    email: string;
    phone_number: string;
    address: string;
    company_id: any;
    status_code: string;
    created_by: number;
    updated_by: number;
    client_ip: string;
    secret_key: string;
    momo_rdr_url: string;
    checkout_properties: string;
    checkout_services: string;
  }

  export interface EmailObject {
    template: string;
    subject: string;
    email: string;
    name: string;
    token?: string;
    referenceCode?: string;
    link?: string;
    company?: string;
    password?: string;
    purpose?: string;
  }
  export interface ClientUserObject {
    client_id: number;
    user_id: number;
  }

  export interface User {
    id: number;
    name: string;
    email: string;
    password: string;
    company_id: number;
    phone_number: string;
    role: string;
    company: any;
  }

  namespace Express {
    export interface Request {
      user: User;
      client: Client;
    }
  }

  namespace NodeJS {
    interface ProcessEnv {
      AUTH_TOKEN_SECRET: string;
      RESET_PASSWORD_TOKEN_SECRET: string;
      COMPLETE_2FA_TOKEN_SECRET: string;
      EMAIL_CHANGE_TOKEN_SECRET: string;
      EMAIL_PASSWORD: string;
      EMAIL_USERNAME: string;
      DB_HOST: string;
      DB_NAME: string;
      DB_USERNAME: string;
      DB_PASSWORD: string;
      CROSS_ORIGIN_URL: string;
      FRONT_END_URL: string;
      PRIMENET_SMS_URL: string;
      PRIMENET_SMS_API_KEY: string;
      ABSA_ACCOUNTS: string;
      ABSA_API_KEY: string;
      MAX_ABSA_TRXN_FETCH_COUNTS: number;
      PRIMENET_MOMO_REFUND_URL: string;
      CARD_TXN: string;
    }
  }
}

import { NextFunction, Request, Response } from "express";
import Logger from "../../helpers/logger";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Service from "./model";

const logger = new Logger();

class ServiceController {
  async createService(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ServiceController",
        method: "createService",
        message:
          "Received create service request: Body ==> " +
          JSON.stringify(req.body),
      });

      const service = await Service.create(req.body);

      logger.info({
        controller: "ServiceController",
        method: "createService",
        message: "Service created",
      });

      res.status(httpStatus.CREATED).json(service);
    } catch (err: any) {
      logger.error({
        controller: "ServiceController",
        method: "createService",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async updateService(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ServiceController",
        method: "updateService",
        message:
          "Received update service request: Body ==> " +
          JSON.stringify(req.body),
      });

      const service = await Service.findByPk(req.params.id);

      if (!service) {
        logger.info({
          controller: "ServiceController",
          method: "updateService",
          message: "Service not found: Service ==> " + req.params.id,
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Service not found"));
      }

      await service.update(req.body);

      res.sendStatus(httpStatus.OK);
    } catch (err: any) {
      logger.error({
        controller: "ServiceController",
        method: "updateService",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async deleteService(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ServiceController",
        method: "deleteService",
        message:
          "Received delete service request: Service ==> " + req.params.id,
      });

      const service = await Service.findByPk(req.params.id);

      if (!service) {
        logger.info({
          controller: "ServiceController",
          method: "deleteService",
          message: "Service not found: Service ==> " + req.params.id,
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Service not found"));
      }

      await service.destroy();

      logger.info({
        controller: "ServiceController",
        method: "deleteService",
        message: "Service deleted",
      });

      res.sendStatus(httpStatus.OK);
    } catch (err: any) {
      logger.error({
        controller: "ServiceController",
        method: "deleteService",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getServices(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ServiceController",
        method: "getServices",
        message: "Received get services request",
      });

      const services = await Service.findAll();

      res.status(httpStatus.OK).json({ data: services });
    } catch (err: any) {
      logger.error({
        controller: "ServiceController",
        method: "getServices",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }
}

export default ServiceController;

import Joi from "joi";

const createClient = Joi.object({
  business_name: Joi.string().required(),
  email: Joi.string().email().required(),
  phone_number: Joi.string().required(),
  address: Joi.string().required(),
  company_id: Joi.string().required(),
}).options({ stripUnknown: true, abortEarly: false });

const updateClient = Joi.object({
  business_name: Joi.string(),
  email: Joi.string().email(),
  address: Joi.string(),
  phone_number: Joi.string(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const updateClientCheckoutServices = Joi.object({
  status: Joi.string().valid("ON", "OFF").required(),
}).options({ stripUnknown: true, abortEarly: true });

const updateClientCheckoutProperties = Joi.object({
  card: Joi.boolean().default(false),
  momo: Joi.boolean().default(false),
  showName: Joi.boolean().default(false),
  showLogo: Joi.boolean().default(false),
  removeFooter: Joi.boolean().default(false),
  momoUrl: Joi.string().default("none"),
  color: Joi.string().default("none"),
  logoUrl: Joi.string().default("none"),
}).options({ stripUnknown: true, abortEarly: true });

const getClients = Joi.object({
  limit: Joi.number().positive().default(20),
  page: Joi.number().positive().default(1),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default {
  createClient,
  getClients,
  updateClient,
  updateClientCheckoutProperties,
  updateClientCheckoutServices,
};

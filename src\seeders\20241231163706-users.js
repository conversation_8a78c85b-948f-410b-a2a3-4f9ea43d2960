"use strict";

const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const companies = await queryInterface.sequelize.query(
      `SELECT id FROM companies`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (!companies.length) {
      throw new Error("No companies found. Seed the companies table first.");
    }

    const users = [];
    for (let i = 1; i <= 1000; i++) {
      const randomCompany =
        companies[Math.floor(Math.random() * companies.length)];

      users.push({
        name: faker.person.fullName(),
        email: faker.internet.email(),
        phone_number: faker.phone.number("+2609########"),
        password: faker.internet.password(10),
        company_id: randomCompany.id,
        client_id: 1,
        status_code: "200",
        token: faker.string.alphanumeric(32),
        token_duration: faker.date.future(),
        created_by: faker.number.int({ min: 1, max: 5 }),
        created_at: new Date(),
        updated_at: new Date(),
        role: faker.helpers.arrayElement(["ACCOUNTS"]),
      });
    }

    return queryInterface.bulkInsert("users", users, {});
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete("users", null, {});
  },
};

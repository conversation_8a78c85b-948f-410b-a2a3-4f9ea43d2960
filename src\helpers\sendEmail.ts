import transporter, { notificationsTransporter } from "../config/email";

export default async function ({
  template,
  subject,
  email,
  name,
  link,
  token,
  company,
  password,
  referenceCode,
  purpose,
  receivers,
  data,
  layout,
}: {
  template: string;
  subject: string;
  email?: string;
  name: string;
  token?: string;
  referenceCode?: string;
  link?: string;
  company?: string;
  password?: string;
  purpose?: string;
  receivers?: string[];
  data?: object;
  layout?: "notifications" | "default";
}) {
  const mail = {
    from: "PrimeNet Gateway <<EMAIL>>",
    to: receivers ? receivers.join(",") : email,
    subject,
    template,
    context: {
      name,
      link,
      token,
      referenceCode,
      email,
      company,
      password,
      purpose,
      data,
    },
  };

  if (layout == "notifications") {
    notificationsTransporter.sendMail(mail);
  } else {
    await transporter.sendMail(mail);
  }
}
export const sendEmailWithAttachment = async function ({
  template,
  subject,
  receivers,
  buffer,
}: {
  template: string;
  subject: string;
  buffer: any;
  receivers: string[];
}) {
  const mail = {
    from: "PrimeNet Gateway <<EMAIL>>",
    to: receivers.join(","),
    subject,
    template,
    context: {
      title: "Recons Report",
      message: "Attached is the Excel file with recons data.",
    },
    attachments: [
      {
        filename: "recons.xlsx",
        content: buffer,
        contentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      },
    ],
  };
  notificationsTransporter.sendMail(mail);
};

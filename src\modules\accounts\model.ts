import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Client from "../client/model";
import ServiceAccounts from "../service_accounts/model";

class Accounts extends Model {
  declare id: any;
  declare float_amount: string;
  declare status_code: string;
  declare client_id: number;
  declare client: any;
  declare created_by: any;
  declare updated_by: any;
  declare created_at: any;
  declare updated_at: any;
}

Accounts.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    float_amount: {
      type: DataTypes.STRING,
    },
    status_code: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    client_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    updated_by: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
  },
  {
    tableName: "accounts",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

Accounts.belongsTo(Client, { foreignKey: "client_id", as: "client" });

export default Accounts;

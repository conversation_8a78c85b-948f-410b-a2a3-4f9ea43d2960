import type { NextFunction, Request, Response } from "express";
import httpStatus from "http-status";
import type { Schema } from "joi";

//TODO: Request Body/Query Params Validator

export default function validateReqData(schema: Schema) {
  return function (req: Request, res: Response, next: NextFunction) {
    const { error, value } = schema.validate(
      !!Object.keys(req.body).length ? req.body : req.query,
      {
        errors: { wrap: { label: "" } },
      }
    );

    if (error) {
      let errors = error.details.map((er) => {
        return { msg: er.message, key: er.context?.key };
      });
      return res
        .status(httpStatus.BAD_REQUEST)
        .json({ details: errors, msg: "Invalid fields" });
    }

    req.body = value;
    next();
  };
}

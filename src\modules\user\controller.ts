import { NextFunction, Request, Response } from "express";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import otpGenerator from "otp-generator";
import { Op, UniqueConstraintError, ValidationErrorItem } from "sequelize";
import sequelize from "../../config/db";
import Logger from "../../helpers/logger";
import PasswordHelper from "../../helpers/password";
import queues from "../../helpers/queues";
import Client from "../client/model";
import ClientUser from "../client_user/model";
import Company from "../company/model";
import User, { EmailChange } from "./model";
import ms from "ms";
import JWTHelper from "../../helpers/jwt";
import Errors from "../../helpers/errors";
import { createClient } from "redis";

const redisClient = createClient({
  socket: {
    host: "127.0.0.1",
    port: 6379,
    connectTimeout: 5000,
  },
});

redisClient.on("error", (err: any) => {
  console.error("Redis error:", err);
});

(async () => {
  try {
    await redisClient.connect();
    console.log("Redis connected");
  } catch (err) {
    console.error("Failed to connect to Redis:", err);
  }
})();

const logger = new Logger();
const jwtHelper = new JWTHelper();
const passwordHelper = new PasswordHelper();

class UserController {
  async getCompanyUsers(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "UserController",
        method: "getCompanyUsers",
        message:
          "Received get company users request: Company ==> " + req.params.id,
      });

      const authUser = req.user;

      const company = await Company.findByPk(req.params.id);

      if (!company) {
        logger.info({
          controller: "UserController",
          method: "getCompanyUsers",
          message: "Company not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "company not found"));
      }

      if (
        authUser.company_id.toString() != req.params.id &&
        authUser.role != "SUPERADMIN"
      ) {
        logger.info({
          controller: "UserController",
          method: "getCompanyUsers",
          message:
            "Superadmin does not belong to the company where they are requesting for users",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      const { count: totalUsers, rows: users } = await User.findAndCountAll({
        ...(req.params.id != "1" && {
          where: {
            company_id: req.params.id,
          },
        }),
        attributes: [
          "id",
          "name",
          "email",
          "phone_number",
          "role",
          "created_at",
        ],
        include: [
          {
            model: Company,
            as: "company",
            attributes: ["id", "name", "email"],
          },
        ],
        offset,
        limit,
      });

      const totalPages = Math.floor(totalUsers / limit)
        ? Math.floor(totalUsers / limit)
        : 1;

      res.status(httpStatus.OK).json({
        data: users,
        current_page: req.body.page,
        total_pages: totalPages,
        total_users: totalUsers,
        first_page: 1,
        next_page:
          req.body.page > totalPages
            ? null
            : req.body.page == totalPages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > totalPages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: totalPages,
      });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "getCompanyUsers",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async searchForUser(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "UserController",
        method: "searchForUser",
        message: "Received search for user request",
      });

      const searchTerm = req.body.query;

      const users = await User.findAll({
        where: {
          [Op.or]: [
            {
              name: {
                [Op.like]: `%${searchTerm}%`,
              },
            },
            {
              phone_number: {
                [Op.like]: `%${searchTerm}%`,
              },
            },
            {
              email: {
                [Op.like]: `%${searchTerm}%`,
              },
            },
          ],
          ...(req.user.role != "SUPERADMIN" && {
            company_id: req.user.company_id,
          }),
        },
        attributes: [
          "id",
          "name",
          "email",
          "phone_number",
          "role",
          "created_at",
        ],
        include: [
          {
            model: Company,
            as: "company",
            attributes: ["id", "name", "email"],
          },
        ],
        limit: 15,
      });

      res.status(httpStatus.OK).json({ data: users });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "searchForUser",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getMyProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user;

      logger.info({
        controller: "UserController",
        method: "getMyProfile",
        message: "Received get my profile: User ==> " + user.id,
      });

      const company = await Company.findByPk(user.company_id);

      // TODO: Fetch all user clients ids

      var userClientIds;

      if (user.role == "ACCOUNTS") {
        userClientIds = (
          await ClientUser.findAll({
            where: { user_id: user.id },
          })
        ).map((data) => data.client_id);
      }

      if (user.role == "ADMIN") {
        userClientIds = (
          await Client.findAll({
            where: { company_id: user.company_id },
            attributes: ["id"],
          })
        ).map((data) => data.id);
      }

      if (user.role == "SUPERADMIN") {
        userClientIds = (await Client.findAll({ attributes: ["id"] })).map(
          (data) => data.id
        );
      }

      logger.info({
        controller: "UserController",
        method: "getMyProfile",
        message: "User client ids: " + JSON.stringify(userClientIds),
      });

      // TODO: Fetch all user clients

      const userClients = await Client.findAll({
        where: {
          id: {
            [Op.in]: userClientIds,
          },
        },
      });

      res.status(httpStatus.OK).json({
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          phone_number: user.phone_number,
          role: user.role,
        },
        company: {
          id: company?.id,
          name: company?.name,
          address: company?.address,
          email: company?.email,
          phone_number: company?.phone_number,
          type_of_business: company?.type_of_business,
          description: company?.description,
          logo_url: company?.logo_url,
        },
        clients: userClients,
      });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "getMyProfile",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong with: Err ==> " + err
        )
      );
    }
  }

  async getUserData(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "UserController",
        method: "getUserData",
        message: "Received get user details: User ==> " + req.params.id,
      });

      const user = await User.findByPk(req.params.id);

      if (!user) {
        logger.info({
          controller: "UserController",
          method: "getUserData",
          message: "User not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "user not found"));
      }

      if (req.user.company_id != user.company_id && req.user.company_id != 1) {
        logger.info({
          controller: "UserController",
          method: "getUserData",
          message: "User does not belong to superadmin company",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const company = await Company.findByPk(user.company_id);

      // TODO: Fetch all user clients ids

      const userClientIds = (
        await ClientUser.findAll({
          where: { user_id: user.id },
        })
      ).map((data) => data.client_id);

      logger.info({
        controller: "UserController",
        method: "getUserData",
        message: "User client ids: " + JSON.stringify(userClientIds),
      });

      // TODO: Fetch all user clients

      const userClients = await Client.findAll({
        where: {
          id: {
            [Op.in]: userClientIds,
          },
        },
        attributes: ["id", "business_name", "address", "phone_number", "email"],
      });

      res.status(httpStatus.OK).json({
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          phone_number: user.phone_number,
          role: user.role,
          created_at: user.created_at,
        },
        company: {
          id: company?.id,
          name: company?.name,
          address: company?.address,
          phone_number: company?.phone_number,
          email: company?.email,
          type_of_business: company?.type_of_business,
          description: company?.description,
          logo_url: company?.logo_url,
        },
        clients: userClients,
      });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "getUserData",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong with: Err ==> " + err
        )
      );
    }
  }

  async updateUser(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "UserController",
        method: "updateUser",
        message: "Received update user: Body ==> " + JSON.stringify(req.body),
      });

      const user = await User.findByPk(req.params.id);

      if (!user) {
        logger.info({
          controller: "UserController",
          method: "updateUser",
          message: "User not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "user not found"));
      }

      if (req.user.company_id != user.company_id && req.user.company_id != 1) {
        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      await user.update(req.body);

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "updateUser",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      if (err instanceof UniqueConstraintError) {
        const _err = err as UniqueConstraintError;

        return res.status(httpStatus.CONFLICT).json(
          _err.errors.map((error: ValidationErrorItem) => {
            return {
              message: error.message,
              field: error.path,
              value: error.value,
            };
          })
        );
      }

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async createUser(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "UserController",
        method: "createUser",
        message: "Received create user: Body ==> " + JSON.stringify(req.body),
      });

      const authUser = req.user;

      if (
        authUser.company_id != req.body.company_id &&
        authUser.role == "SUPERADMIN"
      ) {
        logger.info({
          controller: "UserController",
          method: "createUser",
          message: "Cannot create a user",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const company = await Company.findByPk(req.body.company_id);

      if (!company) {
        logger.info({
          controller: "UserController",
          method: "createUser",
          message: "Company not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Company not found"));
      }

      if (
        authUser.company_id != 1 &&
        req.body.role == "ACCOUNTS" &&
        req.body?.accounts?.length
      ) {
        const clientsCount = await Client.count({
          where: {
            company_id: authUser.company_id,
            id: {
              [Op.in]: req.body.accounts,
            },
          },
        });

        if (clientsCount != req.body.accounts.length) {
          logger.info({
            controller: "UserController",
            method: "createUser",
            message:
              "Tried assigning an account which does not belong to your company or which does not exist",
          });

          return next(
            createHttpError(
              httpStatus.BAD_REQUEST,
              "Tried assigning an account which does not belong to your company or which does not exist"
            )
          );
        }
      }

      const password = otpGenerator.generate(8, {
        digits: true,
        lowerCaseAlphabets: true,
        specialChars: true,
        upperCaseAlphabets: true,
      });

      const hashedPassword = await passwordHelper.hash(password);

      req.body.password = hashedPassword;

      const newUser = await User.create(req.body);

      if (req.body.role == "ACCOUNTS" && req.body?.accounts?.length) {
        //! Adding accounts into queue
        req.body.accounts.forEach(async (account: string) => {
          await queues.clientUserQueue.add({
            user_id: newUser.id,
            client_id: account,
          });
        });
      }

      await queues.emailQueue.add({
        template: "user_created",
        subject: "Primenet User Onboarding",
        email: req.body.email,
        name: newUser.name,
        password,
        company: company?.name,
        link: "https://primenetpay.com/login",
      });

      res.sendStatus(httpStatus.CREATED);
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "createUser",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      if (err instanceof UniqueConstraintError) {
        const _err = err as UniqueConstraintError;

        return res.status(httpStatus.CONFLICT).json(
          _err.errors.map((error: ValidationErrorItem) => {
            return {
              message: error.message,
              field: error.path,
              value: error.value,
            };
          })
        );
      }

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async deleteUser(req: Request, res: Response, next: NextFunction) {
    logger.info({
      controller: "UserController",
      method: "deleteUser",
      message: "Received delete user request: User ==> " + req.params.id,
    });

    const transaction = await sequelize.transaction();

    try {
      const authUser = req.user; //! Current authenticated user

      const userToDelete = await User.findByPk(req.params.id);

      if (!userToDelete) {
        logger.info({
          controller: "UserController",
          method: "deleteUser",
          message: "User does not exist with: User ==> " + req.params.id,
        });

        return next(
          createHttpError(httpStatus.NOT_FOUND, "User does not exist")
        );
      }

      if (authUser.id == userToDelete.id) {
        logger.info({
          controller: "UserController",
          method: "deleteUser",
          message: "User cannot do self deletion",
        });

        return next(
          createHttpError(httpStatus.FORBIDDEN, "You cannot delete yourself")
        );
      }

      if (
        authUser.company_id != userToDelete.company_id &&
        authUser.company_id != 1
      ) {
        logger.info({
          controller: "UserController",
          method: "deleteUser",
          message:
            "You cannot delete this user as the user does not belong to your company",
        });

        return next(
          createHttpError(
            httpStatus.FORBIDDEN,
            "You cannot this user as the user does not belong to your company"
          )
        );
      }

      await userToDelete.destroy({ transaction });
      await ClientUser.destroy({
        where: { user_id: req.params.id },
        transaction,
      });

      await transaction.commit();

      logger.info({
        controller: "UserController",
        method: "deleteUser",
        message: "User deleted successfully: User ==> " + req.params.id,
      });

      res.status(httpStatus.OK).json({ msg: "User deleted successfully" });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "deleteUser",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      await transaction.rollback();

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updatePassword(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "UserController",
        method: "updatePassword",
        message:
          "Received password update request: Body ==> " +
          JSON.stringify(req.body),
      });

      const { new_password, current_password } = req.body;

      const isValidPassword = await passwordHelper.compare({
        hashedPassword: req.user.password,
        password: current_password,
      });

      if (!isValidPassword) {
        logger.info({
          controller: "UserController",
          method: "updatePassword",
          message: "Wrong current password",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Wrong current_password")
        );
      }

      const hashedPassword = await passwordHelper.hash(new_password);

      await User.update(
        { password: hashedPassword },
        { where: { id: req.user.id } }
      );

      logger.info({
        controller: "UserController",
        method: "updatePassword",
        message: "Password updated",
      });

      res.status(httpStatus.OK).json({ msg: "User password updated" });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "updatePassword",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updateProfile(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "UserController",
        method: "updateProfile",
        message:
          "Received profile update request: Body ==> " +
          JSON.stringify(req.body),
      });

      const body = req.body;

      const isValidPassword = await passwordHelper.compare({
        hashedPassword: req.user.password,
        password: body.current_password,
      });

      if (!isValidPassword) {
        logger.info({
          controller: "UserController",
          method: "updateProfile",
          message: "Wrong current password",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, Errors.INCORRECT_PASSWORD)
        );
      }

      if (body?.name) {
        await User.update({ name: body.name }, { where: { id: req.user.id } });
      }

      logger.info({
        controller: "UserController",
        method: "updateProfile",
        message: "Password updated",
      });

      if (body?.email && body.email != req.user.email) {
        const isEmailTaken = await User.findOne({
          where: { email: body.email },
        });

        if (isEmailTaken) {
          logger.info({
            controller: "UserController",
            method: "updateProfile",
            message: "Email is already in use",
          });

          return next(
            createHttpError(httpStatus.CONFLICT, "Email is already in use")
          );
        }

        await EmailChange.destroy({ where: { user_id: req.user.id } });

        const emailChangeToken = jwtHelper.sign({
          payload: { user_id: req.user.id },
          secret: process.env.EMAIL_CHANGE_TOKEN_SECRET!,
          expiresIn: ms("20m"),
        });

        await EmailChange.create({
          token: emailChangeToken,
          user_id: req.user.id,
          email: body.email,
        });

        await queues.emailQueue.add({
          template: "complete_email_change",
          subject: "PrimeNet Solution Email Change Request",
          email: body.email,
          link: `${process.env.FRONT_END_URL}/complete-email-change?token=${emailChangeToken}`,
          name: req.user.name,
        });

        logger.info({
          controller: "AuthController",
          method: "updateProfile",
          message:
            "An email has been sent to your email with email change reset instructions",
        });
      }

      res.status(httpStatus.OK).json({ msg: "Profile updated" });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "updateProfile",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async completeEmailChange(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "UserController",
        method: "completeEmailChange",
        message: "Received Complete Email Change",
      });

      const emailChangeToken = req.params.token;

      const decoded = jwtHelper.verify({
        token: emailChangeToken,
        secret: process.env.EMAIL_CHANGE_TOKEN_SECRET!,
      });

      if (!decoded) {
        logger.info({
          controller: "UserController",
          method: "completeEmailChange",
          message: "Invalid Email Change Token",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Invalid Email Change Token")
        );
      }

      const tokenData = decoded as { user_id: string };

      const isTokenExistInDB = await EmailChange.findOne({
        where: { token: emailChangeToken, user_id: tokenData.user_id },
      });

      if (!isTokenExistInDB) {
        logger.info({
          controller: "UserController",
          method: "completeEmailChange",
          message: "Invalid Reset Password Token",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Invalid Email Change Token")
        );
      }

      await User.update(
        { email: isTokenExistInDB.email },
        { where: { id: tokenData.user_id } }
      );

      await isTokenExistInDB.destroy();

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "completeEmailChange",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async setLoginAttempts(req: Request, res: Response, next: NextFunction) {
    try {
      const { username } = req.body;

      logger.info({
        controller: "UserController",
        method: "setLoginAttempts",
        message: `Received request: username -> ${username}`,
      });

      if (!username) {
        return res.status(400).json({ error: "Username is required" });
      }

      const existingRecord = await redisClient.get(username);
      let updatedData;

      if (existingRecord) {
        const parsed = JSON.parse(existingRecord);

        if (parsed.attempts >= 3) {
          const ttl = await redisClient.ttl(username);
          return res.status(403).json({
            error: "Too many login attempts. Try again later.",
            remainingTime: ttl,
            lastAttempt: parsed.lastAttempt,
          });
        }

        updatedData = {
          username,
          attempts: parsed.attempts + 1,
          lastAttempt: Date.now(),
        };

        if (updatedData.attempts === 3) {
          await redisClient.set(username, JSON.stringify(updatedData), {
            EX: 900,
          });
        } else {
          await redisClient.set(username, JSON.stringify(updatedData));
        }
      } else {
        updatedData = {
          username,
          attempts: 1,
          lastAttempt: Date.now(),
        };

        await redisClient.set(username, JSON.stringify(updatedData));
      }

      return res.status(200).json({
        message: "Login attempt updated",
        data: updatedData,
      });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "setLoginAttempts",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong while setting login attempts"
        )
      );
    }
  }

  async clearLoginAttempts(req: Request, res: Response, next: NextFunction) {
    try {
      const { username } = req.body;

      if (!username) {
        return res.status(400).json({ error: "Username is required" });
      }

      const result = await redisClient.del(username);

      if (result === 0) {
        return res.status(404).json({ message: "No record found to delete" });
      }

      return res
        .status(200)
        .json({ message: "Login attempts cleared successfully" });
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "clearLoginAttempts",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong while clearing login attempts"
        )
      );
    }
  }

  async getLoginAttempts(req: Request, res: Response, next: NextFunction) {
    try {
      const { username } = req.body;

      logger.info({
        controller: "UserController",
        method: "getLoginAttempts",
        message: `Received request: username -> ${username}`,
      });

      if (!username) {
        return res.status(400).json({ message: "Username is required" });
      }

      const existingRecord = await redisClient.get(username);

      if (existingRecord) {
        const parsed = JSON.parse(existingRecord);
        if (parsed.attempts >= 3) {
          const ttl = await redisClient.ttl(username);
          return res.status(403).json({
            message: "Too many login attempts. Try again later.",
            remainingTime: ttl,
            lastAttempt: parsed.lastAttempt,
          });
        } else {
          return res.status(200).json({
            message: "Login attempt exist",
            data: parsed,
          });
        }
      } else {
        return res.status(200).json({
          message: `No Login attempt for ${username}`,
        });
      }
    } catch (err) {
      logger.error({
        controller: "UserController",
        method: "getLoginAttempts",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong while setting login attempts"
        )
      );
    }
  }
}

export default UserController;

import express from "express";
import CompanyController from "./controller";
import { superAdmin, primenetUser } from "../../middlewares/authMiddleware";
import validators from "./validators";
import validateReqData from "../../middlewares/validateReqData";

const router = express();
const controller = new CompanyController();

router
  .route("/")
  .get(
    [primenetUser, validateReqData(validators.getCompaniesValidator)],
    controller.getCompanies
  )
  .post(
    [superAdmin, validateReqData(validators.createCompanyValidator)],
    controller.createCompany
  );

router.get(
  "/search",
  [superAdmin, validateReqData(validators.companySearchValidator)],
  controller.searchForCompany
);

router
  .route("/:id")
  .get(primenetUser, controller.getCompany)
  .delete(superAdmin, controller.deleteCompany)
  .put(
    [superAdmin, validateReqData(validators.updateCompanyValidator)],
    controller.updateCompany
  );

export default router;

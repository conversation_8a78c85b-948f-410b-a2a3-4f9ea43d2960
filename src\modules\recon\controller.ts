/// <reference path="../../types/index.d.ts" />

import { NextFunction, Request, Response } from "express";
import Logger from "../../helpers/logger";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Config, { Recon } from "./model";
import Client from "../client/model";
import { Op, Sequelize } from "sequelize";
import { endOfToday, startOfToday, subHours, format } from "date-fns";
import ExcelJS from "exceljs";
import { toZonedTime } from "date-fns-tz";
import User from "../user/model";
import { sendEmailWithAttachment } from "../../helpers/sendEmail";

const logger = new Logger();
class ReconController {
  async createConfig(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ReconController",
        method: "createConfig",
        message: "Received create config request: Body ==> " + req.body,
      });

      const client = await Client.findByPk(req.body.client_id);

      if (!client) {
        logger.info({
          controller: "ReconController",
          method: "createConfig",
          message: "Client not found",
        });
        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      req.body.created_by = req.user.id;
      const config = await Config.create(req.body);

      res.status(httpStatus.CREATED).json(config);
    } catch (err) {
      logger.error({
        controller: "ReconController",
        method: "createConfig",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updateConfig(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ReconController",
        method: "updateConfig",
        message: "Update config request: Config ==> " + req.params.id,
      });

      const config = await Config.findByPk(req.params.id);

      if (!config) {
        logger.info({
          controller: "ReconController",
          method: "updateConfig",
          message: "Config not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Config not found"));
      }

      await config.update(req.body);

      res.status(httpStatus.OK).json({ msg: "Config updated successfully" });
    } catch (err) {
      logger.error({
        controller: "ReconController",
        method: "updateConfig",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updateSettlementStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      logger.info({
        controller: "ReconController",
        method: "updateSettlementStatus",
        message:
          "Update settlement status request: Config ==> " + req.params.id,
      });

      if (req.body.status == "SETTLED" && req.user.role != "FINANCE") {
        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const recon = await Recon.findByPk(req.params.id);

      if (!recon) {
        logger.info({
          controller: "ReconController",
          method: "updateSettlementStatus",
          message: "Recon not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Recon not found"));
      }

      await recon.update({ settlement_status: req.body.status });

      res.status(httpStatus.OK).json({ msg: "Recon updated successfully" });
    } catch (err) {
      logger.error({
        controller: "ReconController",
        method: "updateSettlementStatus",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updateReconsStatus(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ReconController",
        method: "getTransactionsByFilter",
        message:
          "Received get recons by filter request: BODY ==> " +
          JSON.stringify(req.body),
      });

      if (
        req.body.status == "SETTLED" &&
        req.user.role != "FINANCE" &&
        req.user.role != "SUPERADMIN"
      ) {
        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const fromDate = req.body.from_date
        ? `${req.body.from_date} ${req.body.from_date_time}`
        : null;

      const toDate = req.body.to_date
        ? `${req.body.to_date} ${req.body.to_date_time}`
        : null;

      const clients = req.body.clients ?? [];

      await Recon.update(
        { settlement_status: req.body.status },
        {
          where: {
            ...(clients.length && {
              client_id: {
                [Op.in]: clients,
              },
            }),
            ...(fromDate &&
              toDate && {
                created_at: {
                  [Op.between]: [fromDate, toDate],
                },
              }),
            ...(req.body.account_number && {
              account_number: {
                [Op.like]: `%${req.body.account_number}%`,
              },
            }),
            ...(req.body.recon_type && {
              recon_type: req.body.recon_type,
            }),
            ...(req.body.currency && {
              currency: req.body.currency,
            }),
          },
        }
      );

      if (req.body.status == "PENDING") {
        const emails = (await User.findAll({ where: { role: "FINANCE" } })).map(
          (users) => users.email
        );

        if (emails.length < 1) {
          res.sendStatus(httpStatus.OK);
        }

        const recons = await Recon.findAll({
          where: {
            ...(clients.length && {
              client_id: {
                [Op.in]: clients,
              },
            }),
            ...(fromDate &&
              toDate && {
                created_at: {
                  [Op.between]: [fromDate, toDate],
                },
              }),
            ...(req.body.account_number && {
              account_number: {
                [Op.like]: `%${req.body.account_number}%`,
              },
            }),
            ...(req.body.recon_type && {
              recon_type: req.body.recon_type,
            }),
            ...(req.body.currency && {
              currency: req.body.currency,
            }),
          },
          include: [
            { model: Client, as: "client", attributes: ["business_name"] },
          ],
        });

        const clientMap: Record<string, any> = {};
        recons.forEach((recon: any) => {
          const clientId = recon.client_id;
          if (!clientMap[clientId]) {
            clientMap[clientId] = {
              business_name: recon.client.business_name,
              total_collected: 0,
              total_refunds: 0,
              mobile_commission: 0,
              card_commission: 0,
              primenet_commission: 0,
              partner_commission: 0,
              mno_commission: 0,
              merchant_momo_settlement: 0,
              merchant_card_settlement: 0,
            };
          }

          // Aggregate numeric values
          clientMap[clientId].total_collected +=
            Number(recon.total_collected) || 0;
          clientMap[clientId].total_refunds += Number(recon.total_refunds) || 0;
          clientMap[clientId].mobile_commission +=
            Number(recon.mobile_commission) || 0;
          clientMap[clientId].card_commission +=
            Number(recon.card_commission) || 0;
          clientMap[clientId].primenet_commission +=
            Number(recon.primenet_commission) || 0;
          clientMap[clientId].partner_commission +=
            Number(recon.partner_commission) || 0;
          clientMap[clientId].mno_commission +=
            Number(recon.mno_commission) || 0;
          clientMap[clientId].merchant_momo_settlement +=
            Number(recon.merchant_momo_settlement) || 0;
          clientMap[clientId].merchant_card_settlement +=
            Number(recon.merchant_card_settlement) || 0;
        });

        const workbook = new ExcelJS.Workbook();

        const worksheet = workbook.addWorksheet("Recons");

        worksheet.columns = [
          { header: "Merchant", key: "merchant", width: 20 },
          { header: "Total Collected", key: "totalCollected", width: 15 },
          { header: "Total Refunds", key: "totalRefunds", width: 15 },
          { header: "Mobile Commission", key: "mobileCommission", width: 20 },
          { header: "Card Commission", key: "cardCommission", width: 30 },
          {
            header: "Merchant Mobile Settlement",
            key: "merchantMobileSettlement",
            width: 20,
          },
          {
            header: "Merchant Card Settlement",
            key: "merchantCardSettlement",
            width: 10,
          },
        ];

        Object.values(clientMap).forEach((clientData: any) => {
          worksheet.addRow([
            clientData.business_name,
            clientData.total_collected,
            clientData.total_refunds,
            clientData.mobile_commission,
            clientData.card_commission,
            clientData.merchant_momo_settlement,
            clientData.merchant_card_settlement,
          ]);
        });

        const buffer = await workbook.xlsx.writeBuffer();

        await sendEmailWithAttachment({
          receivers: emails,
          buffer,
          subject: "Recons Pending Settlement",
          template: "recons",
        });
      }

      res.sendStatus(httpStatus.OK);
    } catch (err: any) {
      logger.error({
        controller: "ReconController",
        method: "getTransactionsByFilter",
        message: "An error occured with: Err ==> " + err?.message,
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getRecons(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "getRecons",
        method: "getRecons",
        message:
          "Received Recons request: Query strings ==> " +
          JSON.stringify(req.body),
      });

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      const startTime = startOfToday();
      const endTime = endOfToday();

      const [recons, reconsSummary] = await Promise.all([
        Recon.findAll({
          where: {
            created_at: {
              [Op.between]: [startTime, endTime],
            },
            ...(req.body.recon_type && {
              recon_type: req.body.recon_type,
            }),
            ...(req.body.currency && {
              currency: req.body.currency,
            }),
          },
          offset,
          limit,
          order: [["created_at", "DESC"]],
          include: [
            { model: Client, as: "client", attributes: ["business_name"] },
          ],
        }),
        Recon.findOne({
          attributes: [
            [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
            [
              Sequelize.fn("SUM", Sequelize.col("total_collected")),
              "totalCollected",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("mobile_commission")),
              "totalMobileCommission",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("card_commission")),
              "totalCardCommission",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("partner_commission")),
              "totalPartnerCommission",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("primenet_commission")),
              "totalPrimeNetCommission",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("mno_commission")),
              "totalMnoCommission",
            ],
          ],
          where: {
            created_at: {
              [Op.between]: [startTime, endTime],
            },
            ...(req.body.recon_type && {
              recon_type: req.body.recon_type,
            }),
            ...(req.body.currency && {
              currency: req.body.currency,
            }),
          },
        }),
      ]);

      const totalPages =
        Math.ceil(reconsSummary?.dataValues.totalCount / limit) || 1;

      res.status(httpStatus.OK).json({
        data: recons.map((txn: Recon) => {
          return {
            ...txn.dataValues,
            updated_at: subHours(new Date(txn.dataValues.updated_at), 2),
          };
        }),
        summary: reconsSummary,
        current_page: req.body.page,
        total_pages: totalPages,
        first_page: 1,
        next_page:
          req.body.page > totalPages
            ? null
            : req.body.page == totalPages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > totalPages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: totalPages,
      });
    } catch (err) {
      logger.error({
        controller: "ReconController",
        method: "getRecons",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getReconsByFilter(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ReconController",
        method: "getTransactionsByFilter",
        message:
          "Received get recons by filter request: BODY ==> " +
          JSON.stringify(req.body),
      });

      const fromDate = req.body.from_date
        ? `${req.body.from_date} ${req.body.from_date_time}`
        : null;

      const toDate = req.body.to_date
        ? `${req.body.to_date} ${req.body.to_date_time}`
        : null;

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;
      const clients = req.body.clients ?? [];

      const [recons, reconsSummary] = await Promise.all([
        Recon.findAll({
          where: {
            ...(clients.length && {
              client_id: {
                [Op.in]: clients,
              },
            }),
            ...(fromDate &&
              toDate && {
                created_at: {
                  [Op.between]: [fromDate, toDate],
                },
              }),
            ...(req.body.status && { settlement_status: req.body.status }),
            ...(req.body.recon_type && {
              recon_type: req.body.recon_type,
            }),
            ...(req.body.account_number && {
              account_number: {
                [Op.like]: `%${req.body.account_number}%`,
              },
            }),
            ...(req.body.currency && {
              currency: req.body.currency,
            }),
          },
          offset,
          limit,
          order: [["created_at", "DESC"]],
          include: [
            { model: Client, as: "client", attributes: ["business_name"] },
          ],
        }),
        Recon.findOne({
          attributes: [
            [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
            [
              Sequelize.fn("SUM", Sequelize.col("total_collected")),
              "totalCollected",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("mobile_commission")),
              "totalMobileCommission",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("card_commission")),
              "totalCardCommission",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("partner_commission")),
              "totalPartnerCommission",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("primenet_commission")),
              "totalPrimeNetCommission",
            ],
            [
              Sequelize.fn("SUM", Sequelize.col("mno_commission")),
              "totalMnoCommission",
            ],
          ],
          where: {
            ...(clients.length && {
              client_id: {
                [Op.in]: clients,
              },
            }),
            ...(fromDate &&
              toDate && {
                created_at: {
                  [Op.between]: [fromDate, toDate],
                },
              }),
            ...(req.body.status && { settlement_status: req.body.status }),
            ...(req.body.recon_type && {
              recon_type: req.body.recon_type,
            }),
            ...(req.body.account_number && {
              account_number: {
                [Op.like]: `%${req.body.account_number}%`,
              },
            }),
            ...(req.body.currency && {
              currency: req.body.currency,
            }),
          },
        }),
      ]);

      const totalPages =
        Math.ceil(reconsSummary?.dataValues.totalCount / limit) || 1;

      res.status(httpStatus.OK).json({
        data: recons.map((txn: Recon) => {
          return {
            ...txn.dataValues,
            updated_at: subHours(new Date(txn.dataValues.updated_at), 2),
          };
        }),
        summary: reconsSummary,
        current_page: req.body.page,
        total_pages: totalPages,
        first_page: 1,
        next_page:
          req.body.page > totalPages
            ? null
            : req.body.page == totalPages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > totalPages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: totalPages,
      });
    } catch (err: any) {
      logger.error({
        controller: "ReconController",
        method: "getTransactionsByFilter",
        message: "An error occured with: Err ==> " + err?.message,
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getRecon(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ReconController",
        method: "getRecon",
        message: "Received request to get a recon: Recon ==> " + req.params.id,
      });

      const config = await Recon.findByPk(req.params.id, {
        include: [
          { model: Client, as: "client", attributes: ["business_name"] },
        ],
      });

      if (!config) {
        logger.info({
          controller: "ReconController",
          method: "getRecon",
          message: "Recon not found",
        });
        return next(createHttpError(httpStatus.NOT_FOUND, "Recon not found"));
      }

      res.status(httpStatus.OK).json(config);
    } catch (err: any) {
      logger.error({
        controller: "ReconController",
        method: "getRecon",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getConfigs(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ReconController",
        method: "getConfigs",
        message:
          "Received get configs request: Query strings ==> " +
          JSON.stringify(req.body),
      });

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      const { count: totalConfigs, rows: configs } =
        await Config.findAndCountAll({
          offset,
          limit,
          include: [
            { model: Client, as: "client", attributes: ["business_name"] },
          ],
        });

      const totalPages = Math.floor(totalConfigs / limit)
        ? Math.floor(totalConfigs / limit)
        : 1;

      res.status(httpStatus.OK).json({
        data: configs,
        current_page: req.body.page,
        total_pages: totalPages,
        total: totalConfigs,
        first_page: 1,
        next_page:
          req.body.page > totalPages
            ? null
            : req.body.page == totalPages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > totalPages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: totalPages,
      });
    } catch (err) {
      logger.error({
        controller: "ReconController",
        method: "getConfigs",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getConfig(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ReconController",
        method: "getConfig",
        message:
          "Received request to get a config: Config ==> " + req.params.id,
      });

      const config = await Config.findByPk(req.params.id);

      if (!config) {
        logger.info({
          controller: "ReconController",
          method: "getConfig",
          message: "Config not found",
        });
        return next(createHttpError(httpStatus.NOT_FOUND, "Config not found"));
      }

      res.status(httpStatus.OK).json(config);
    } catch (err: any) {
      logger.error({
        controller: "ReconController",
        method: "getConfig",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async exportRecons(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ReconController",
        method: "exportRecons",
        message:
          "Received export recons request: BODY ==> " +
          JSON.stringify(req.body),
      });

      const fromDate = req.body.from_date
        ? `${req.body.from_date} ${req.body.from_date_time}`
        : startOfToday();

      const toDate = req.body.to_date
        ? `${req.body.to_date} ${req.body.to_date_time}`
        : endOfToday();

      let clients = req.body.clients ?? [];

      logger.info({
        controller: "ReconController",
        method: "exportRecons",
        message: "Fetching recons",
      });

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader("Content-Disposition", "attachment; filename=recons.xlsx");

      const workbook = new ExcelJS.stream.xlsx.WorkbookWriter({
        stream: res,
        useStyles: true,
        useSharedStrings: true,
      });

      const worksheet = workbook.addWorksheet("Recons");

      worksheet.columns = [
        { header: "Merchant", key: "merchant", width: 20 },
        { header: "Account Number", key: "accountNumber", width: 20 },
        { header: "Account Name", key: "accountName", width: 20 },
        { header: "Total Collected", key: "totalCollected", width: 15 },
        { header: "Total Refunds", key: "totalRefunds", width: 15 },
        { header: "Mobile Commission", key: "mobileCommission", width: 20 },
        { header: "Card Commission", key: "cardCommission", width: 30 },
        { header: "PrimeNet Commission", key: "primeNetCommission", width: 20 },
        { header: "Partner Commission", key: "partnerCommission", width: 20 },
        { header: "MNO Commission", key: "mnoCommission", width: 15 },
        {
          header: "Merchant Mobile Settlement",
          key: "merchantMobileSettlement",
          width: 20,
        },
        {
          header: "Merchant Card Settlement",
          key: "merchantCardSettlement",
          width: 10,
        },
        { header: "Settlement Status", key: "settlementStatus", width: 30 },
        { header: "Created At", key: "createdAt", width: 20 },
      ];

      const limit = 50000;
      let offset = 0;
      let hasMore = true;
      let batchNumber = 1;

      logger.info({
        controller: "ReconController",
        method: "exportRecons",
        message: "Fetching Recons",
      });

      while (hasMore) {
        logger.info({
          controller: "ReconController",
          method: "exportRecons",
          message: "Recons Batch NO: " + batchNumber,
        });

        const recons = await Recon.findAll({
          where: {
            ...(clients.length && {
              client_id: {
                [Op.in]: clients,
              },
            }),
            ...(fromDate &&
              toDate && {
                created_at: {
                  [Op.between]: [fromDate, toDate],
                },
              }),
            ...(req.body.status && { settlement_status: req.body.status }),
            ...(req.body.account_number && {
              account_number: {
                [Op.like]: `%${req.body.account_number}%`,
              },
            }),
            ...(req.body.recon_type && {
              recon_type: req.body.recon_type,
            }),
            ...(req.body.currency && {
              currency: req.body.currency,
            }),
          },
          offset,
          limit,
          order: [["created_at", "DESC"]],
          include: [
            { model: Client, as: "client", attributes: ["business_name"] },
          ],
        });

        if (recons.length === 0) {
          hasMore = false;
          break;
        }

        recons.forEach((item: any) => {
          worksheet
            .addRow([
              item.client.business_name,
              item.dataValues.account_number,
              item.dataValues.account_name,
              +item.dataValues.total_collected,
              +item.dataValues.total_refunds,
              +item.dataValues.mobile_commission,
              +item.dataValues.card_commission,
              +item.dataValues.primenet_commission,
              +item.dataValues.partner_commission,
              +item.dataValues.mno_commission,
              +item.dataValues.merchant_momo_settlement,
              +item.dataValues.merchant_card_settlement,
              item.dataValues.settlement_status,
              format(
                toZonedTime(
                  new Date(item.dataValues.created_at),
                  "Africa/Lusaka"
                ),
                "dd/MM/yyyy, h:mm:ss a"
              ),
            ])
            .commit();
        });

        offset += limit;
        batchNumber += 1;
      }

      logger.info({
        controller: "ReconController",
        method: "exportRecons",
        message: "Completed Fetching Recons",
      });

      worksheet.commit();
      await workbook.commit();
      res.end();
    } catch (err: any) {
      logger.error({
        controller: "ReconController",
        method: "exportRecons",
        message: "An error occurred: Err ==> " + err?.message,
      });
      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }
}

export default ReconController;

import { NextFunction, Request, Response } from "express";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Logger from "../../helpers/logger";
import Company from "./model";
import Client from "../client/model";
import ClientUser from "../client_user/model";
import { Op, UniqueConstraintError, ValidationErrorItem } from "sequelize";
import User from "../user/model";
import sequelize from "../../config/db";

const logger = new Logger();

class CompanyController {
  async createCompany(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "CompanyController",
        method: "createCompany",
        message:
          "Received create company request: Body ==> " +
          JSON.stringify(req.body),
      });

      const company = await Company.create(req.body);

      logger.info({
        controller: "CompanyController",
        method: "createCompany",
        message: "Company created successfully",
      });

      res.status(httpStatus.OK).json(company);
    } catch (err) {
      logger.error({
        controller: "CompanyController",
        method: "createCompany",
        message: "An error occured with: Err ===> " + JSON.stringify(err),
      });

      if (err instanceof UniqueConstraintError) {
        const _err = err as UniqueConstraintError;

        return res.status(httpStatus.CONFLICT).json(
          _err.errors.map((error: ValidationErrorItem) => {
            return {
              message: error.message,
              field: error.path,
              value: error.value,
            };
          })
        );
      }

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updateCompany(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "CompanyController",
        method: "updateCompany",
        message:
          "Received update company request: Body ==> " +
          JSON.stringify(req.body),
      });

      const company = await Company.findByPk(req.params.id);

      if (!company) {
        logger.info({
          controller: "CompanyController",
          method: "updateCompany",
          message: "Company not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Company not found"));
      }

      await company.update(req.body);

      logger.info({
        controller: "CompanyController",
        method: "updateCompany",
        message: "Company updated successfully",
      });

      res.status(httpStatus.OK).json({ msg: "Company updated successfully" });
    } catch (err) {
      logger.error({
        controller: "CompanyController",
        method: "updateCompany",
        message: "An error occured with: Err ===> " + JSON.stringify(err),
      });

      if (err instanceof UniqueConstraintError) {
        const _err = err as UniqueConstraintError;

        return res.status(httpStatus.CONFLICT).json(
          _err.errors.map((error: ValidationErrorItem) => {
            return {
              message: error.message,
              field: error.path,
              value: error.value,
            };
          })
        );
      }

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getCompanies(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "CompanyController",
        method: "getCompanies",
        message:
          "Received get companies request: Query strings ==> " +
          JSON.stringify(req.body),
      });

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      const { count: totalCompanies, rows: companies } =
        await Company.findAndCountAll({
          offset,
          limit,
        });

      const totalPages = Math.floor(totalCompanies / limit)
        ? Math.floor(totalCompanies / limit)
        : 1;

      res.status(httpStatus.OK).json({
        data: companies,
        current_page: req.body.page,
        total_pages: totalPages,
        first_page: 1,
        next_page:
          req.body.page > totalPages
            ? null
            : req.body.page == totalPages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > totalPages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: totalPages,
      });
    } catch (err) {
      logger.error({
        controller: "CompanyController",
        method: "getCompanies",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getCompany(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "CompanyController",
        method: "getCompany",
        message:
          "Received request to get a company: Company ==> " + req.params.id,
      });

      const company = await Company.findByPk(req.params.id);

      if (!company) {
        logger.info({
          controller: "CompanyController",
          method: "getCompany",
          message: "Company not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Company not found"));
      }

      res.status(httpStatus.OK).json(company);
    } catch (err: any) {
      logger.error({
        controller: "CompanyController",
        method: "getCompany",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async deleteCompany(req: Request, res: Response, next: NextFunction) {
    logger.info({
      controller: "CompanyController",
      method: "deleteCompany",
      message: "Received delete company request",
    });

    const transaction = await sequelize.transaction();
    try {
      const company = await Company.findByPk(req.params.id);

      if (!company) {
        logger.info({
          controller: "CompanyController",
          method: "deleteCompany",
          message: "Company not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Company not found"));
      }

      const clients = await Client.findAll({
        where: { company_id: req.params.id },
      });

      const clientsIds = clients.map((client) => client.id);

      await ClientUser.destroy({
        where: {
          client_id: {
            [Op.in]: clientsIds,
          },
        },
        transaction,
      });

      await User.destroy({
        where: { company_id: req.params.id },
        transaction,
      });

      await Client.destroy({
        where: { company_id: req.params.id },
        transaction,
      });

      await company.destroy({ transaction });

      await transaction.commit();

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        controller: "CompanyController",
        method: "deleteCompany",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      await transaction.rollback();

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async searchForCompany(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "CompanyController",
        method: "searchForCompany",
        message: "Received search for company request",
      });

      const searchTerm = req.body.query;

      const companies = await Company.findAll({
        where: {
          [Op.or]: [
            {
              name: {
                [Op.like]: `%${searchTerm}%`,
              },
            },
            {
              phone_number: {
                [Op.like]: `%${searchTerm}%`,
              },
            },
            {
              email: {
                [Op.like]: `%${searchTerm}%`,
              },
            },
          ],
        },
        attributes: ["id", "name", "email"],
        limit: 15,
      });

      res.status(httpStatus.OK).json({ data: companies });
    } catch (err) {
      logger.error({
        controller: "CompanyController",
        method: "searchForCompany",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }
}

export default CompanyController;

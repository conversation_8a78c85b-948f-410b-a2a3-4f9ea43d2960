import winston from "winston";
const { combine, prettyPrint, timestamp, json } = winston.format;

const catTimestamp = new Date().toLocaleString("en-US", {
  year: "numeric",
  month: "2-digit",
  day: "2-digit",
  hour: "2-digit",
  minute: "2-digit",
  second: "2-digit",
  timeZone: "Africa/Lusaka",
  hour12: false, // Use 24-hour format
});

class Logger {
  logger = winston.createLogger({
    format: combine(prettyPrint(), timestamp({ format: catTimestamp }), json()),
    transports: [
      new winston.transports.Console(),
      new winston.transports.File({ filename: "express.log" }),
    ],
  });

  info(data: { message: string; method?: string; controller?: string }) {
    this.logger.info(data.message, {
      ...(data?.method && { method: data.method }),
      ...(data?.controller && { controller: data.controller }),
    });
  }
  error(data: { message: string; method?: string; controller?: string }) {
    this.logger.error(data.message, {
      ...(data?.method && { method: data.method }),
      ...(data?.controller && { controller: data.controller }),
    });
  }
}

export default Logger;

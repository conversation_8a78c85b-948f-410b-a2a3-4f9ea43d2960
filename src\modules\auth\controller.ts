import { NextFunction, Request, Response } from "express";
import otpGenerator from "otp-generator";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Logger from "../../helpers/logger";
import PasswordHelper from "../../helpers/password";
import <PERSON><PERSON><PERSON>el<PERSON> from "../../helpers/jwt";
import HelperFunctions from "../../helpers";
import ms from "ms";
import User from "../user/model";
import { PasswordReset } from "./model";
import queues from "../../helpers/queues";
import Company from "../company/model";
import ClientUser from "../client_user/model";
import Client from "../client/model";
import { Op } from "sequelize";

const logger = new Logger();
const passwordHelper = new PasswordHelper();
const jwtHelper = new JWTHelper();

class AuthController {
  async checkUser(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "AuthController",
        method: "checkUser",
        message: "Received check user request: User ==> " + req.body.email,
      });

      const user = await User.findOne({ where: { email: req.body.email } });

      if (!user) {
        logger.info({
          controller: "AuthController",
          method: "checkUser",
          message: "User with the provided email does not exist",
        });

        return next(
          createHttpError(
            httpStatus.NOT_FOUND,
            "User with the provided email does not exist"
          )
        );
      }

      return res.status(httpStatus.OK).json({ name: user.name });
    } catch (err) {
      logger.error({
        controller: "AuthController",
        method: "checkUser",
        message: "An error occured with: Err ===> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async login(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "AuthController",
        method: "login",
        message: "Received login request: Body ==> " + JSON.stringify(req.body),
      });

      const user = await User.findOne({ where: { email: req.body.email } });

      if (!user) {
        logger.info({
          controller: "AuthController",
          method: "login",
          message: "User with the provided email does not exist",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Invalid credentials")
        );
      }

      const isValidPassword = await passwordHelper.compare({
        hashedPassword: user.password,
        password: req.body.password,
      });

      if (!isValidPassword) {
        logger.info({
          controller: "AuthController",
          method: "login",
          message: "Email and password do not match",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Invalid credentials")
        );
      }

      const token = otpGenerator.generate(6, {
        digits: true,
        lowerCaseAlphabets: false,
        specialChars: false,
        upperCaseAlphabets: false,
      });

      const referenceCode = otpGenerator.generate(5, {
        digits: false,
        lowerCaseAlphabets: true,
        specialChars: false,
        upperCaseAlphabets: true,
      });

      const complete2faToken = jwtHelper.sign({
        payload: { user: user.id },
        secret: process.env.COMPLETE_2FA_TOKEN_SECRET!,
        expiresIn: ms("20m"),
      });

      const token_duration = Date.now() + ms("20m");

      await user.update({ token, token_duration });

      await queues.emailQueue.add({
        template: "complete_2fa",
        subject: "PrimeNet Solution Identification Code",
        email: req.body.email,
        name: user.name,
        token,
        referenceCode,
      });

      res.cookie("CTFA_TOKEN", complete2faToken, {
        httpOnly: true,
        maxAge: ms("20m"),
      });

      logger.info({
        controller: "AuthController",
        method: "login",
        message: "An email has been sent to your email with verification token",
      });

      res.status(httpStatus.OK).json({
        msg: "An email has been sent to your email with verification token",
        referenceCode,
      });
    } catch (err) {
      logger.error({
        controller: "AuthController",
        method: "login",
        message: "An error occured with: Err ===> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async complete2fa(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "AuthController",
        method: "complete2fa",
        message: "Received complete 2fa request: Body ==> " + req.body,
      });

      const complete2faToken = req.cookies.CTFA_TOKEN;

      const decoded = jwtHelper.verify({
        token: complete2faToken,
        secret: process.env.COMPLETE_2FA_TOKEN_SECRET!,
      });

      if (!decoded) {
        logger.info({
          controller: "AuthController",
          method: "complete2fa",
          message: "Invalid Complete 2fa token",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Invalid complete 2fa token")
        );
      }

      const tokenData = decoded as { user: string };

      const user = await User.findByPk(tokenData.user);

      if (!user) {
        logger.info({
          controller: "AuthController",
          method: "complete2fa",
          message: "User not found",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Invalid complete 2fa token")
        );
      }

      if (
        user.token != req.body.token ||
        HelperFunctions.hasTokenExpired(user.token_duration)
      ) {
        logger.info({
          controller: "AuthController",
          method: "complete2fa",
          message: "Invalid otp token",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Invalid otp token")
        );
      }

      const company = await Company.findByPk(user.company_id);

      // TODO: Fetch all user clients ids

      var userClientIds;

      if (user.role == "ACCOUNTS" && user.company_id != 1) {
        userClientIds = (
          await ClientUser.findAll({
            where: { user_id: user.id },
          })
        ).map((data) => data.client_id);
      }

      if (user.role == "ADMIN" && user.company_id != 1) {
        userClientIds = (
          await Client.findAll({
            where: { company_id: user.company_id },
            attributes: ["id"],
          })
        ).map((data) => data.id);
      }

      if (user.company_id == 1) {
        userClientIds = (await Client.findAll({ attributes: ["id"] })).map(
          (data) => data.id
        );
      }

      logger.info({
        controller: "AuthController",
        method: "complete2fa",
        message: "User client ids: " + JSON.stringify(userClientIds),
      });

      // TODO: Fetch all user clients

      const userClients = await Client.findAll({
        where: {
          id: {
            [Op.in]: userClientIds ?? [],
          },
        },
      });

      const authToken = jwtHelper.sign({
        payload: { user: tokenData.user },
        secret: process.env.AUTH_TOKEN_SECRET!,
        expiresIn: ms("1h"),
      });

      res.cookie("AUTH_TOKEN", authToken, {
        httpOnly: true,
        maxAge: ms("1h"),
      });

      res.clearCookie("CTFA_TOKEN");

      logger.info({
        controller: "AuthController",
        method: "complete2fa",
        message: "User logged in successfully",
      });

      res.status(httpStatus.OK).json({
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          phone_number: user.phone_number,
          role: user.role,
        },
        company: {
          id: company?.id,
          name: company?.name,
          address: company?.address,
          email: company?.email,
          phone_number: company?.phone_number,
          type_of_business: company?.type_of_business,
          description: company?.description,
          logo_url: company?.logo_url,
        },
        clients: userClients,
      });
    } catch (err) {
      logger.error({
        controller: "AuthController",
        method: "complete2fa",
        message: "An error occured with: Err ===> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async forgotPassword(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "AuthController",
        method: "forgotPassword",
        message:
          "Received forgot password request: Body ===> " +
          JSON.stringify(req.body),
      });

      const user = await User.findOne({
        where: { email: req.body.email },
      });

      if (!user) {
        logger.info({
          controller: "AuthController",
          method: "forgotPassword",
          message: "User with the provided email does not exist",
        });

        return next(
          createHttpError(httpStatus.NOT_FOUND, "User does not exist")
        );
      }

      await PasswordReset.destroy({ where: { email: user.email } });

      const resetPasswordToken = jwtHelper.sign({
        payload: { email: user.email },
        secret: process.env.RESET_PASSWORD_TOKEN_SECRET!,
        expiresIn: ms("20m"),
      });

      await PasswordReset.create({
        token: resetPasswordToken,
        email: user.email,
      });

      await queues.emailQueue.add({
        template: "reset_password",
        subject: "PrimeNet Solution Password Reset",
        email: user.email,
        link: `${process.env.FRONT_END_URL}/complete-forgot-password?token=${resetPasswordToken}`,
        name: user.name,
      });

      logger.info({
        controller: "AuthController",
        method: "forgotPassword",
        message:
          "An email has been sent to your email with password reset instructions",
      });

      res.status(httpStatus.OK).json({
        msg: "An email has been sent to your email with password reset instructions",
      });
    } catch (err) {
      logger.info({
        controller: "AuthController",
        method: "forgotPassword",
        message: "An error occured with: Err ===> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async resetPassword(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "AuthController",
        method: "resetPassword",
        message:
          "Received reset password request: Body ===> " +
          JSON.stringify(req.body),
      });

      const resetPasswordToken = req.params.token;

      const decoded = jwtHelper.verify({
        token: resetPasswordToken,
        secret: process.env.RESET_PASSWORD_TOKEN_SECRET!,
      });

      if (!decoded) {
        logger.info({
          controller: "AuthController",
          method: "resetPassword",
          message: "Invalid complete 2fa token",
        });

        return next(
          createHttpError(httpStatus.BAD_REQUEST, "Invalid complete 2fa token")
        );
      }

      const tokenData = decoded as { email: string };

      const isTokenExistInDB = await PasswordReset.findOne({
        where: { token: resetPasswordToken, email: tokenData.email },
      });

      if (!isTokenExistInDB) {
        logger.info({
          controller: "AuthController",
          method: "resetPassword",
          message: "Invalid Reset Password Token",
        });

        return next(
          createHttpError(
            httpStatus.BAD_REQUEST,
            "Invalid Reset Password Token"
          )
        );
      }

      const newHashedPassword = await passwordHelper.hash(
        req.body.new_password
      );

      await User.update(
        { password: newHashedPassword },
        { where: { email: tokenData.email } }
      );

      await isTokenExistInDB.destroy();

      res.status(httpStatus.OK).json({ msg: "Password updated successfully" });
    } catch (err) {
      logger.info({
        controller: "AuthController",
        method: "resetPassword",
        message: "An error occured with: Err ===> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }
}

export default AuthController;

import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Client from "../client/model";

class A<PERSON><PERSON><PERSON> extends Model {
  declare id: any;
  declare name: string;
  declare key: string;
  declare client_id: number;
  declare client: any;
}

ApiKey.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
    },
    key: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    client_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
  },
  {
    tableName: "api_keys",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

ApiKey.belongsTo(Client, { foreignKey: "client_id", as: "client" });

export default ApiKey;

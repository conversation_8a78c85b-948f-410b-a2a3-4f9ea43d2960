import Joi from "joi";

const getTransactions = Joi.object({
  limit: Joi.number().integer().min(1).max(20).default(15),
  page: Joi.number().integer().default(1),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const reconcileTransaction = Joi.object({
  status_code: Joi.string().valid("301", "300", "283", "304", "303").required(),
  response_message: Joi.string().required(),
  external_id: Joi.string().required(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const refundTransaction = Joi.object({
  external_id: Joi.string().required(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const getTransactionsByFilter = Joi.object({
  from_date: Joi.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  from_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("00:00"),
  to_date: Joi.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  to_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("23:59"),
  phone_number: Joi.string().allow(""),
  account_number: Joi.string().allow(""),
  transaction_type: Joi.string().allow(""),
  service_id: Joi.string().allow(""),
  transaction_id: Joi.string().allow(""),
  external_id: Joi.string().allow(""),
  client_id: Joi.string().allow(""),
  status_code: Joi.string().allow(""),
  narration: Joi.string().allow(""),
  currency: Joi.string().valid("ZMW", "USD").allow(""),
  limit: Joi.number().integer().min(1).max(20).default(15),
  page: Joi.number().integer().default(1),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const exportTransactions = Joi.object({
  from_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .allow(""),
  from_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("00:00")
    .allow(""),
  to_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .allow(""),
  to_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("23:59")
    .allow(""),
  phone_number: Joi.string().allow(""),
  account_number: Joi.string().allow(""),
  transaction_type: Joi.string().allow(""),
  service_id: Joi.string().allow(""),
  transaction_id: Joi.string().allow(""),
  external_id: Joi.string().allow(""),
  client_id: Joi.string().allow(""),
  status_code: Joi.string().allow(""),
  narration: Joi.string().allow(""),
  currency: Joi.string().valid("ZMW", "USD").allow(""),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default {
  getTransactions,
  getTransactionsByFilter,
  reconcileTransaction,
  exportTransactions,
  refundTransaction,
};

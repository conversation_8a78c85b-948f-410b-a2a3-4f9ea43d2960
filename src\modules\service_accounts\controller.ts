import { Request, Response, NextFunction } from "express";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import ExcelJS from "exceljs";
import Logger from "../../helpers/logger";
import ServiceAccounts from "./model";
import { Op, Sequelize } from "sequelize";
import ClientUser from "../client_user/model";
import Service from "../service/model";
import Account from "../accounts/model";
import Client from "../client/model";
import { clientCallbackQueue } from "../../helpers/queues";
import StatusCode from "../status_code/model";
import {
  endOfToday,
  endOfYesterday,
  format,
  startOfToday,
  startOfYesterday,
  subHours,
} from "date-fns";
import { toZonedTime } from "date-fns-tz";
import redis from "../../config/redis";
import ms from "ms";
import ApiKey from "../api_key/model";
import axios from "axios";
import Accounts from "../accounts/model";

const logger = new Logger();

class ServiceAccountsController {

  async getClientServiceAccounts(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const clientId = req.params.clientId;

      logger.info({
        controller: "ServiceAccountsController",
        method: "getClientServiceAccounts",
        message: `Received request to get Service Accounts: Client ID ==> ${clientId}`,
      });

      const clientData = await Client.findOne({
        where: { id: clientId },
      });

      if (!clientData) {
        return next(
          createHttpError(httpStatus.NOT_FOUND, "Client user not found")
        );
      }
      const accountData = await Accounts.findOne({
        where: { client_id: clientId },
      });
      logger.info({
        controller: "ServiceAccountsController",
        method: "getClientServiceAccounts",
        message: `Account data ==> ${accountData}`,
      });
      const data = await ServiceAccounts.findAll({
        where: {
          client_id: clientData.id,
        },
        order: [["created_at", "DESC"]],
        include: [
          { model: Client, as: "client", attributes: ["business_name"] },
          { model: Service, as: "service", attributes: ["name"] },
        ],
      });

      res.status(httpStatus.OK).json({
        money: accountData?.float_amount,
        billers: data.map((txn: ServiceAccounts) => ({
          ...txn.dataValues
        }))
      });
      
    } catch (err) {
      logger.error({
        controller: "ServiceAccountsController",
        method: "getClientServiceAccounts",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }
}

export default ServiceAccountsController;

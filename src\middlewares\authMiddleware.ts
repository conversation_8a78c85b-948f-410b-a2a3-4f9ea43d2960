/// <reference path="../types/index.d.ts" />

import type { Request } from "express";
import jwt, { JsonWebTokenError } from "jsonwebtoken";
import httpStatus from "http-status";
import { Response, NextFunction } from "express";
import Logger from "../helpers/logger";
import getAuthToken from "../helpers/getAuthToken";
import User from "../modules/user/model";
import ClientUser from "../modules/client_user/model";
import createHttpError from "http-errors";
import Client from "../modules/client/model";
import ApiKey from "../modules/api_key/model";
import Company from "../modules/company/model";

const logger = new Logger();

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = getAuthToken(req);

    if (!token) {
      return res.sendStatus(httpStatus.UNAUTHORIZED);
    }

    const decoded = jwt.verify(token, process.env.AUTH_TOKEN_SECRET!);

    const tokenData = decoded as { user: string };

    const user = await User.findByPk(tokenData.user, {
      include: [{ model: Company, as: "company" }],
    });

    if (!user) {
      return res.sendStatus(httpStatus.UNAUTHORIZED);
    }

    req.user = user;

    next();
  } catch (err) {
    if (err instanceof JsonWebTokenError) {
      logger.error({ message: "Invalid auth token" });
      return res.sendStatus(httpStatus.UNAUTHORIZED);
    }

    logger.error({
      message: "An error occured with: Err ===> " + JSON.stringify(err),
    });

    return res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR);
  }
};

export const apiKeyAuthentication = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const apiKey = req.headers["x-authorization"];

    if (!apiKey) {
      return res.sendStatus(httpStatus.UNAUTHORIZED);
    }

    const dbApiKey = await ApiKey.findOne({ where: { key: apiKey } });

    if (!dbApiKey) {
      return res.sendStatus(httpStatus.UNAUTHORIZED);
    }

    req.body.client_id = dbApiKey.client_id;

    next();
  } catch (err) {
    logger.error({
      message: "An error occured with: Err ===> " + JSON.stringify(err),
    });

    return res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR);
  }
};

export const AbsaAuthentication = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const apiKey = req.headers["x-authorization-key"];
    const platformKey = req.headers["x-platform-key"];
    const clientKey = req.headers["x-client-key"];

    logger.info({
      message: `x-authorization-key ${apiKey} | x-platform-key: ${platformKey} | x-client-key: ${clientKey}`,
    });

    if (
      process.env.ABSA_API_KEY != apiKey ||
      platformKey != "MOMO_PAYMENTS" ||
      clientKey != "ABSA_ZM"
    ) {
      return res.sendStatus(httpStatus.UNAUTHORIZED);
    }

    next();
  } catch (err) {
    logger.error({
      message: "An error occured with: Err ===> " + JSON.stringify(err),
    });

    return res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR);
  }
};

export const primenetUser = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  return authenticate(req, res, function () {
    if (req.user?.company_id != 1) {
      return res.sendStatus(httpStatus.FORBIDDEN);
    }
    next();
  });
};

export const superAdmin = (req: Request, res: Response, next: NextFunction) => {
  return authenticate(req, res, function () {
    if (req.user.company_id != 1 || req.user.role != "SUPERADMIN") {
      logger.info({ message: "Not a superadmin" });
      return res.sendStatus(httpStatus.FORBIDDEN);
    }
    next();
  });
};

export const reconTeam = (req: Request, res: Response, next: NextFunction) => {
  return authenticate(req, res, function () {
    const allowedRoles = ["SUPERADMIN", "FINANCE", "RECON_COORDINATOR"];
    if (!allowedRoles.includes(req.user.role)) {
      logger.info({ message: "Not recon team member" });
      return res.sendStatus(httpStatus.FORBIDDEN);
    }
    next();
  });
};

export const companyAdmin = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  return authenticate(req, res, function () {
    if (
      !req.user?.company_id ||
      (req.user?.role != "ADMIN" && req.user.role != "SUPERADMIN")
    ) {
      return res.sendStatus(httpStatus.FORBIDDEN);
    }
    next();
  });
};

export const clientUser = (req: Request, res: Response, next: NextFunction) => {
  if (req.headers["x-authorization"]) {
    return apiKeyAuthentication(req, res, next);
  }

  return authenticate(req, res, async function () {
    var client_id = req.body?.client_id ?? req.params.id;

    logger.info({ message: "CLIENT ID ==> " + client_id });

    const client = await Client.findByPk(client_id);

    if (!client) {
      logger.error({ message: "Client not found" });
      return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
    }

    const isClientUser = await ClientUser.findOne({
      where: { client_id, user_id: req.user.id },
    });

    const isPrimenetUser = req.user.company_id == 1;

    const isClientCompanyAdmin =
      req.user.company_id == client.company_id && req.user.role == "ADMIN";

    if (!isClientUser && !isPrimenetUser && !isClientCompanyAdmin) {
      return res.sendStatus(httpStatus.FORBIDDEN);
    }

    req.client = client;

    next();
  });
};

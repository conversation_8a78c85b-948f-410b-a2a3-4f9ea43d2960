import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Client from "../client/model";
import Service from "../service/model";
import StatusCode from "../status_code/model";

class Transaction extends Model {
  declare id: any;
  declare client_id: any;
  declare service_id: any;
  declare amount: any;
  declare phone_number: any;
  declare account_number: any;
  declare status_code: any;
  declare currency: any;
  declare transaction_type: any;
  declare transaction_id: any;
  declare external_id: any;
  declare callback_sent_at: any;
  declare callback: any;
  declare response_message: any;
  declare response_code: any;
  declare pre_collection_details_id: any;
  declare narration: any;
  declare merchant_id: any;
  declare created_by: any;
  declare updated_by: any;
  declare created_at: any;
  declare updated_at: any;
}

Transaction.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    client_id: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    service_id: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    amount: {
      type: DataTypes.STRING,
    },
    currency: {
      type: DataTypes.STRING,
    },
    transaction_type: {
      type: DataTypes.STRING,
    },
    transaction_id: {
      type: DataTypes.STRING,
    },
    external_id: {
      type: DataTypes.STRING,
    },
    phone_number: {
      type: DataTypes.STRING,
    },
    account_number: {
      type: DataTypes.STRING,
    },
    response_message: {
      type: DataTypes.STRING,
    },
    response_code: {
      type: DataTypes.STRING,
    },
    status_code: {
      type: DataTypes.STRING,
    },
    narration: {
      type: DataTypes.STRING,
    },
    merchant_id: {
      type: DataTypes.STRING,
    },
    callback: {
      type: DataTypes.CHAR,
    },
    callback_sent_at: {
      type: DataTypes.DATE,
    },
    pre_collection_details_id: {
      type: DataTypes.CHAR,
    },
    created_by: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    updated_by: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
  },
  {
    tableName: "transactions",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        name: "idx_transaction_client_id",
        fields: ["client_id"],
      },
      {
        name: "idx_transaction_created_at",
        fields: ["created_at"],
      },
      {
        name: "idx_client_created_at",
        fields: ["client_id", "created_at"],
      },
      {
        name: "idx_transaction_status_code",
        fields: ["status_code"],
      },
      {
        name: "idx_transaction_created_at_status_code",
        fields: ["created_at", "status_code"],
      },
    ],
  }
);

Transaction.belongsTo(Client, {
  foreignKey: "client_id",
  as: "client",
  constraints: false,
});
Transaction.belongsTo(Service, { foreignKey: "service_id", as: "service" });

Transaction.belongsTo(StatusCode, {
  foreignKey: "status_code",
  targetKey: "status_code",
  as: "statusCode",
});

export default Transaction;

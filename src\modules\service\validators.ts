import Joi from "joi";

const createService = Joi.object({
  name: Joi.string().trim().required(),
  min_amount: Joi.string().trim().required(),
  max_amount: Joi.string().trim().required(),
  code: Joi.string().trim(),
  description: Joi.string().trim(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const updateService = Joi.object({
  name: Joi.string().trim(),
  min_amount: Joi.string().trim(),
  max_amount: Joi.string().trim(),
  code: Joi.string().trim(),
  description: Joi.string().trim(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default { createService, updateService };

/// <reference path="types/index.d.ts" />

import app from "./app";
import Queue from "bull";
import sequelize from "./config/db";
import sendEmail from "./helpers/sendEmail";
import Logger from "./helpers/logger";
import "./crons/recon";
import "./crons/transactions";

const PORT = process.env.PORT;

const emailQueue = new Queue("emailQueue");

const logger = new Logger();

app.listen(PORT, async () => {
  try {
    await sequelize.authenticate();

    emailQueue.process(async function (job, done) {
      try {
        logger.info({ message: "Running Email Job" });

        const data = job.data as EmailObject;

        logger.info({ message: "data ===> " + JSON.stringify(data) });

        await sendEmail({
          template: data.template,
          subject: data.subject,
          email: data.email,
          name: data.name,
          token: data?.token,
          link: data?.link,
          company: data?.company,
          password: data?.password,
        });

        done();

        logger.info({ message: "Email sent successfully" });
      } catch (err) {
        logger.error({
          message: "Error sending email: Err ===> " + JSON.stringify(err),
        });
      }
    });

    console.log("Database connection established");

    console.log("App running on port: " + PORT);
  } catch (err) {
    console.log("Failed to connect to database");
  }
});

import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";

class PasswordReset extends Model {
  declare id: any;
  declare email: any;
  declare token: any;
}

PasswordReset.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    token: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    tableName: "password_resets",
    sequelize,
    createdAt: "created_at",
    updatedAt: false,
  }
);

export { PasswordReset };

/// <reference path="../../types/index.d.ts" />

import { NextFunction, Request, Response } from "express";
import Logger from "../../helpers/logger";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Client from "./model";
import Company from "../company/model";
import ClientUser from "../client_user/model";
import sequelize from "../../config/db";
import User from "../user/model";
import { UniqueConstraintError, ValidationErrorItem } from "sequelize";

const logger = new Logger();

class ClientController {
  async createClient(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientController",
        method: "createClient",
        message: "Received create client request: Body ==> " + req.body,
      });

      const company = await Company.findByPk(req.body.company_id);

      if (!company) {
        logger.info({
          controller: "ClientController",
          method: "createClient",
          message: "Company not found",
        });
        return next(createHttpError(httpStatus.NOT_FOUND, "Company not found"));
      }

      req.body.created_by = req.user.id;
      const client = await Client.create(req.body);

      res.status(httpStatus.CREATED).json(client);
    } catch (err) {
      logger.error({
        controller: "ClientController",
        method: "createClient",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      if (err instanceof UniqueConstraintError) {
        const _err = err as UniqueConstraintError;

        return res.status(httpStatus.CONFLICT).json(
          _err.errors.map((error: ValidationErrorItem) => {
            return {
              message: error.message,
              field: error.path,
              value: error.value,
            };
          })
        );
      }

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updateClient(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientController",
        method: "updateClient",
        message: "Update client request: Client ==> " + req.params.id,
      });

      const client = await Client.findByPk(req.params.id);

      if (!client) {
        logger.info({
          controller: "ClientController",
          method: "updateClient",
          message: "Client not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      await client.update(req.body);

      res.status(httpStatus.OK).json({ msg: "Client updated successfully" });
    } catch (err) {
      logger.error({
        controller: "ClientController",
        method: "updateClient",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      if (err instanceof UniqueConstraintError) {
        const _err = err as UniqueConstraintError;

        return res.status(httpStatus.CONFLICT).json(
          _err.errors.map((error: ValidationErrorItem) => {
            return {
              message: error.message,
              field: error.path,
              value: error.value,
            };
          })
        );
      }

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updateClientCheckoutServices(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      logger.info({
        controller: "ClientController",
        method: "updateClientCheckoutServices",
        message:
          "Update client checkout services request: Body ==> " +
          JSON.stringify(req.body),
      });

      const client = await Client.findByPk(req.params.id);

      if (!client) {
        logger.info({
          controller: "ClientController",
          method: "updateClientCheckoutServices",
          message: "Client not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      await client.update({ checkout_services: req.body.status });

      logger.info({
        controller: "ClientController",
        method: "updateClientCheckoutServices",
        message: "Client checkout services updated",
      });

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        controller: "ClientController",
        method: "updateClientCheckoutServices",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async updateClientCheckoutProperties(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      logger.info({
        controller: "ClientController",
        method: "updateClientCheckoutProperties",
        message:
          "Update client checkout properties request: Body ==> " +
          JSON.stringify(req.body),
      });

      const client = await Client.findByPk(req.params.id);

      if (!client) {
        logger.info({
          controller: "ClientController",
          method: "updateClientCheckoutProperties",
          message: "Client not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      await client.update({ checkout_properties: req.body });

      logger.info({
        controller: "ClientController",
        method: "updateClientCheckoutProperties",
        message: "Client checkout properties updated",
      });

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        controller: "ClientController",
        method: "updateClientCheckoutProperties",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getClients(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientController",
        method: "getClients",
        message:
          "Received get clients request: Query strings ==> " +
          JSON.stringify(req.body),
      });

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      const { count: totalClients, rows: clients } =
        await Client.findAndCountAll({
          offset,
          limit,
        });

      const totalPages = Math.floor(totalClients / limit)
        ? Math.floor(totalClients / limit)
        : 1;

      res.status(httpStatus.OK).json({
        data: clients.map((client) => {
          if (client.checkout_properties) {
            return {
              ...client.dataValues,
              checkout_properties: JSON.parse(client.checkout_properties),
            };
          }

          return client;
        }),
        current_page: req.body.page,
        total_pages: totalPages,
        first_page: 1,
        next_page:
          req.body.page > totalPages
            ? null
            : req.body.page == totalPages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > totalPages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: totalPages,
      });
    } catch (err) {
      logger.error({
        controller: "ClientController",
        method: "getClients",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getClient(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientController",
        method: "getClient",
        message:
          "Received request to get a client: Client ==> " + req.params.id,
      });

      const client = await Client.findByPk(req.params.id);

      if (!client) {
        logger.info({
          controller: "ClientController",
          method: "getClient",
          message: "Client not found",
        });
        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      if (client?.checkout_properties) {
        client.checkout_properties = JSON.parse(client.checkout_properties);
      }

      res.status(httpStatus.OK).json(client);
    } catch (err: any) {
      logger.error({
        controller: "ClientController",
        method: "getClient",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async deleteClient(req: Request, res: Response, next: NextFunction) {
    logger.info({
      controller: "ClientController",
      method: "deleteClient",
      message: "Received delete client request: Client ==> " + req.params.id,
    });

    const transaction = await sequelize.transaction();
    try {
      const client = await Client.findByPk(req.params.id);

      if (!client) {
        logger.info({
          controller: "ClientController",
          method: "deleteClient",
          message: "Client not found",
        });
        return next(
          createHttpError(httpStatus.NOT_FOUND, "Client does not exist")
        );
      }

      await ClientUser.destroy({
        where: { client_id: req.params.id },
        transaction,
      });

      await User.destroy({
        where: { client_id: req.params.id },
        transaction,
      });

      await client.destroy({ transaction });

      await transaction.commit();

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        controller: "ClientController",
        method: "deleteClient",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      await transaction.rollback();

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }
}

export default ClientController;

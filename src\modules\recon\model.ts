import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Client from "../client/model";

class ReconConfig extends Model {
  declare id: number;
  declare client_id: number;
  declare account_number: string;
  declare account_name: string;
  declare momo_comm_rates: string;
  declare card_comm_rates: string;
  declare bank_commission: string;
  declare model: string;
  declare email: string;
  declare revenue_share: boolean;
  declare primenet_revenue_share: string;
  declare created_by: number;
  declare updated_by: number;
}

class Recon extends Model {
  declare id: number;
  declare client_id: number;
  declare account_number: string;
  declare account_name: string;
  declare momo_comm_rates: string;
  declare card_comm_rates: string;
  declare bank_commission: string;
  declare model: string;
  declare total_collected: string;
  declare total_refunds: string;
  declare settlement_status: string;
  declare recon_type: string;
  declare currency: string;
  declare revenue_share: boolean;
  declare primenet_revenue_share: string;
  declare merchant_momo_settlement: string;
  declare merchant_card_settlement: string;
  declare partner_commission: string;
  declare mobile_commission: string;
  declare card_commission: string;
  declare primenet_commission: string;
  declare mno_commission: string;
  declare created_by: number;
  declare updated_by: number;
}

ReconConfig.init(
  {
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    client_id: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
    },
    account_number: {
      type: DataTypes.STRING,
    },
    account_name: {
      type: DataTypes.STRING,
    },
    momo_comm_rates: {
      type: DataTypes.STRING,
    },
    card_comm_rates: {
      type: DataTypes.STRING,
    },
    bank_commission: {
      type: DataTypes.STRING,
    },
    revenue_share: {
      type: DataTypes.BOOLEAN,
    },
    primenet_revenue_share: {
      type: DataTypes.STRING,
    },
    model: {
      type: DataTypes.STRING,
    },
    email: {
      type: DataTypes.STRING,
    },
    created_by: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
    updated_by: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
  },
  {
    tableName: "reconConfigs",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

Recon.init(
  {
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    client_id: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
    },
    account_number: {
      type: DataTypes.STRING,
    },
    account_name: {
      type: DataTypes.STRING,
    },
    momo_comm_rates: {
      type: DataTypes.STRING,
    },
    card_comm_rates: {
      type: DataTypes.STRING,
    },
    model: {
      type: DataTypes.STRING,
    },
    settlement_status: {
      type: DataTypes.STRING,
    },
    total_collected: {
      type: DataTypes.STRING,
    },
    total_refunds: {
      type: DataTypes.STRING,
    },
    recon_type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING,
    },
    revenue_share: {
      type: DataTypes.BOOLEAN,
    },
    primenet_revenue_share: {
      type: DataTypes.STRING,
    },
    merchant_momo_settlement: {
      type: DataTypes.STRING,
    },
    merchant_card_settlement: {
      type: DataTypes.STRING,
    },
    partner_commission: {
      type: DataTypes.STRING,
    },
    mobile_commission: {
      type: DataTypes.STRING,
    },
    card_commission: {
      type: DataTypes.STRING,
    },
    primenet_commission: {
      type: DataTypes.STRING,
    },
    mno_commission: {
      type: DataTypes.STRING,
    },
    bank_commission: {
      type: DataTypes.STRING,
    },
    created_by: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
    updated_by: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
  },
  {
    tableName: "recons",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

ReconConfig.belongsTo(Client, { foreignKey: "client_id", as: "client" });
Recon.belongsTo(Client, { foreignKey: "client_id", as: "client" });

export { Recon };

export default ReconConfig;

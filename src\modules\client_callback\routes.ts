import express from "express";
import ClientCallbackController from "./controller";
import { companyAdmin, superAdmin } from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();
const controller = new ClientCallbackController();

router.post(
  "/",
  [superAdmin, validateReqData(validators.createClientCallback)],
  controller.createClientCallback
);

router.get("/client/:id", companyAdmin, controller.getClientCallbacks);
router.delete("/:id", companyAdmin, controller.deleteClientCallback);

export default router;

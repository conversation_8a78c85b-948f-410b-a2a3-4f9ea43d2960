import Joi from "joi";

const loginValidator = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required(),
}).options({ abortEarly: false, stripUnknown: true });

const complete2faValidator = Joi.object({
  token: Joi.string().required(),
}).options({ abortEarly: false, stripUnknown: true });

const emailValidator = Joi.object({
  email: Joi.string().email().required(),
}).options({ abortEarly: false, stripUnknown: true });

const resetPasswordValidator = Joi.object({
  new_password: Joi.string().required(),
}).options({ abortEarly: false, stripUnknown: true });

export default {
  loginValidator,
  emailValidator,
  complete2faValidator,
  resetPasswordValidator,
};

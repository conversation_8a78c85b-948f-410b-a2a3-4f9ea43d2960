import { Op } from "sequelize";
import Transaction from "../modules/transaction/model";
import ReconConfig, { Recon } from "../modules/recon/model";
import HelperFunctions from "../helpers";
import { startOfYesterday, endOfYesterday } from "date-fns";
import { CronJob } from "cron";
import Logger from "../helpers/logger";
import Tariff from "../modules/tariff/model";

// Initialize logger
const logger = new Logger();

type TransactionHandler = (tx: any, isRefund: boolean) => void;

const instance = process.env.NODE_APP_INSTANCE || 0;

if (instance == 0) {
  CronJob.from({
    cronTime: "0 2 * * *",
    onTick: async () => {
      logger.info({
        controller: "ReconciliationCronJob",
        method: "onTick",
        message: "Cron job triggered",
      });

      try {
        await ReconciliationCronJob.runDailyReconciliation();
        logger.info({
          controller: "Reconciliation<PERSON>ronJob",
          method: "onTick",
          message: "Daily reconciliation completed successfully",
        });
      } catch (error) {
        console.log(error);

        logger.error({
          controller: "ReconciliationCronJob",
          method: "onTick",
          message: "Cron job failed",
        });
      }
    },
    start: true,
    timeZone: "Africa/Lusaka",
  });
}

class ReconciliationCronJob {
  static async runDailyReconciliation() {
    const startTime = startOfYesterday();
    const endTime = endOfYesterday();
    const batchSize = 15;
    let configPageOffset = 0;
    let hasConfigs = true;

    logger.info({
      controller: "ReconciliationCronJob",
      method: "runDailyReconciliation",
      message: `Starting reconciliation for date range ${startTime.toISOString()} to ${endTime.toISOString()}`,
    });

    while (hasConfigs) {
      const configs = await ReconConfig.findAll({
        limit: batchSize,
        offset: configPageOffset,
      });

      if (configs.length === 0) {
        logger.info({
          controller: "ReconciliationCronJob",
          method: "runDailyReconciliation",
          message: "No more configurations to process",
        });
        hasConfigs = false;
        break;
      }

      for (const config of configs) {
        logger.info({
          controller: "ReconciliationCronJob",
          method: "runDailyReconciliation",
          message: `Processing config ID=${config.id} for client ${config.client_id}`,
        });

        let totalMerchantMobileSettlement = 0;
        let totalPrimeNetCommission = 0;
        let totalMnoCommission = 0;

        let totalCollected = 0;
        let totalMobileCommission = 0;
        let totalPartnerCommission = 0;
        let totalRefunds = 0;

        // Track card transactions by currency
        const cardTransactionsByCurrency: Record<
          string,
          {
            totalMerchantCardSettlement: number;
            totalCardCommission: number;
            totalBankCommission: number;
            totalPrimeNetCommission: number;
            totalCollected: number;
            totalRefunds: number;
          }
        > = {};

        const tarrif = await Tariff.findOne({
          where: {
            [Op.or]: [
              {
                charge_type: "FIXED_PERCENTAGE",
                client_id: config.client_id,
                ...(config.account_number && {
                  account_number: config.account_number,
                }),
              },
              {
                charge_type: "FIXED_AMOUNT",
                client_id: config.client_id,
                ...(config.account_number && {
                  account_number: config.account_number,
                }),
              },
            ],
          },
        });

        const surchargePct = tarrif?.percent
          ? parseFloat(tarrif.percent.replace("%", "")) / 100
          : 0;

        const momoRate = config.momo_comm_rates
          ? parseFloat(config.momo_comm_rates.replace("%", "")) / 100
          : 0;

        const cardRate = config.card_comm_rates
          ? parseFloat(config.card_comm_rates.replace("%", "")) / 100
          : 0;

        const bankRate = config.bank_commission
          ? parseFloat(config.bank_commission.replace("%", "")) / 100
          : 0;

        const primeNetShareRate = config.primenet_revenue_share
          ? parseFloat(config.primenet_revenue_share.replace("%", "")) / 100
          : 0;

        // process collections and refunds
        await processTransactionBatches(
          config,
          startTime,
          endTime,
          batchSize,
          (transaction, isRefund) => {
            const amount = parseFloat(transaction.amount.toString());

            if (isRefund) {
              totalRefunds += amount;
            }

            let surchargeAmount = 0;
            let actualAmount = amount;

            if (tarrif) {
              if (tarrif.charge_type == "FIXED_PERCENTAGE") {
                actualAmount = amount / (1 + surchargePct);
                surchargeAmount = amount - actualAmount;
              } else if (tarrif.charge_type == "FIXED_AMOUNT") {
                surchargeAmount = +tarrif.fixed_amount;
                actualAmount = amount - surchargeAmount;
              }
            }

            // Card transactions
            if (transaction.service_id == process.env.CARD_TXN) {
              const commission = amount * cardRate;
              const currency = transaction.currency || "ZMW"; // Default to ZMW if no currency

              // Initialize currency tracking if not exists
              if (!cardTransactionsByCurrency[currency]) {
                cardTransactionsByCurrency[currency] = {
                  totalMerchantCardSettlement: 0,
                  totalCardCommission: 0,
                  totalBankCommission: 0,
                  totalPrimeNetCommission: 0,
                  totalCollected: 0,
                  totalRefunds: 0,
                };
              }

              const currencyData = cardTransactionsByCurrency[currency];

              currencyData.totalMerchantCardSettlement += isRefund
                ? -amount
                : amount - commission;
              currencyData.totalPrimeNetCommission += isRefund
                ? -(commission - commission * bankRate)
                : commission - commission * bankRate;
              currencyData.totalBankCommission += isRefund
                ? -(commission * bankRate)
                : commission * bankRate;
              currencyData.totalCardCommission += commission;
              currencyData.totalCollected += amount;

              if (isRefund) {
                currencyData.totalRefunds += amount;
              }
            } else {
              totalCollected += amount;

              // Mobile transactions
              const mnoCharge = HelperFunctions.getCommission(transaction);

              if (config.revenue_share && surchargeAmount) {
                const txnCharge = actualAmount * momoRate;
                const commission = txnCharge - mnoCharge + surchargeAmount;
                const primeNetShare = commission * primeNetShareRate;
                const partnerShare = commission - primeNetShare;

                totalMerchantMobileSettlement += isRefund
                  ? -(actualAmount - txnCharge)
                  : actualAmount - txnCharge;

                totalPartnerCommission += isRefund
                  ? -partnerShare
                  : partnerShare;

                totalPrimeNetCommission += isRefund
                  ? -primeNetShare
                  : primeNetShare;

                totalMobileCommission += isRefund
                  ? -(txnCharge + surchargeAmount)
                  : txnCharge + surchargeAmount;
              } else if (config.revenue_share && !surchargeAmount) {
                const txnCharge = actualAmount * momoRate;
                const commission = txnCharge - mnoCharge;
                const primeNetShare = commission * primeNetShareRate;
                const partnerShare = commission - primeNetShare;

                totalMerchantMobileSettlement += isRefund
                  ? -(actualAmount - txnCharge)
                  : actualAmount - txnCharge;

                totalPartnerCommission += isRefund
                  ? -partnerShare
                  : partnerShare;

                totalPrimeNetCommission += isRefund
                  ? -primeNetShare
                  : primeNetShare;

                totalMobileCommission += isRefund ? -txnCharge : txnCharge;
              } else if (!config.revenue_share && surchargeAmount) {
                const commission = surchargeAmount - mnoCharge;

                totalMerchantMobileSettlement += isRefund
                  ? -actualAmount
                  : actualAmount;

                totalPrimeNetCommission += isRefund ? -commission : commission;

                totalMobileCommission += isRefund
                  ? -surchargeAmount
                  : surchargeAmount;
              } else {
                const txnCharge = actualAmount * momoRate;

                totalMerchantMobileSettlement += isRefund
                  ? -(actualAmount - txnCharge)
                  : actualAmount - txnCharge;

                totalPrimeNetCommission += isRefund
                  ? -(txnCharge - mnoCharge)
                  : txnCharge - mnoCharge;

                totalMobileCommission += isRefund ? -txnCharge : txnCharge;
              }

              totalMnoCommission += isRefund ? -mnoCharge : mnoCharge;
            }
          }
        );

        // Create mobile recon record only if there are mobile transactions
        const reconPromises: Promise<any>[] = [];

        if (
          totalCollected > 0 ||
          totalRefunds > 0 ||
          totalMobileCommission > 0
        ) {
          const mobileReconData = {
            client_id: config.client_id,
            account_name: config.account_name,
            account_number: config.account_number,
            momo_comm_rates: config.momo_comm_rates,
            total_collected: totalCollected.toFixed(2),
            total_refunds: totalRefunds.toFixed(2),
            primenet_revenue_share: config.primenet_revenue_share,
            primenet_commission: totalPrimeNetCommission.toFixed(2),
            mno_commission: totalMnoCommission.toFixed(2),
            merchant_momo_settlement: totalMerchantMobileSettlement.toFixed(2),
            partner_commission: totalPartnerCommission.toFixed(2),
            mobile_commission: totalMobileCommission.toFixed(2),
            settlement_status: "RECONCILED",
            recon_type: "MOMO",
            currency: "ZMW", // Mobile transactions are typically in ZMW
            created_by: 1,
          };

          logger.info({
            controller: "ReconciliationCronJob",
            method: "runDailyReconciliation",
            message: `Creating mobile recon record for client ${config.client_id} with total_collected: ${totalCollected}`,
          });

          reconPromises.push(Recon.create(mobileReconData));
        } else {
          logger.info({
            controller: "ReconciliationCronJob",
            method: "runDailyReconciliation",
            message: `Skipping mobile recon record for client ${config.client_id} - no mobile transactions found`,
          });
        }

        // Create card recon records for each currency (only if there are transactions)
        Object.entries(cardTransactionsByCurrency).forEach(
          ([currency, currencyData]) => {
            // Only create recon record if there are actual transactions
            if (
              currencyData.totalCollected > 0 ||
              currencyData.totalRefunds > 0 ||
              currencyData.totalCardCommission > 0
            ) {
              const cardReconData = {
                client_id: config.client_id,
                account_name: config.account_name,
                account_number: config.account_number,
                card_comm_rates: config.card_comm_rates,
                bank_commission: config.bank_commission,
                total_collected: currencyData.totalCollected.toFixed(2),
                total_refunds: currencyData.totalRefunds.toFixed(2),
                primenet_revenue_share: config.primenet_revenue_share,
                primenet_commission:
                  currencyData.totalPrimeNetCommission.toFixed(2),
                merchant_card_settlement:
                  currencyData.totalMerchantCardSettlement.toFixed(2),
                partner_commission: totalPartnerCommission.toFixed(2),
                card_commission: currencyData.totalCardCommission.toFixed(2),
                settlement_status: "RECONCILED",
                recon_type: "CARD",
                currency: currency,
                created_by: 1,
              };

              logger.info({
                controller: "ReconciliationCronJob",
                method: "runDailyReconciliation",
                message: `Creating card recon record for client ${config.client_id}, currency: ${currency}, total_collected: ${currencyData.totalCollected}`,
              });

              reconPromises.push(Recon.create(cardReconData));
            } else {
              logger.info({
                controller: "ReconciliationCronJob",
                method: "runDailyReconciliation",
                message: `Skipping card recon record for client ${config.client_id}, currency: ${currency} - no card transactions found`,
              });
            }
          }
        );

        // Execute all recon record creations
        if (reconPromises.length > 0) {
          await Promise.all(reconPromises);
          logger.info({
            controller: "ReconciliationCronJob",
            method: "runDailyReconciliation",
            message: `Created ${reconPromises.length} recon records for client ${config.client_id}`,
          });
        } else {
          logger.info({
            controller: "ReconciliationCronJob",
            method: "runDailyReconciliation",
            message: `No recon records created for client ${config.client_id} - no transactions found`,
          });
        }
      }

      configPageOffset += batchSize;
    }
  }
}

async function processTransactionBatches(
  config: ReconConfig,
  startTime: Date,
  endTime: Date,
  batchSize: number,
  transactionHandler: TransactionHandler
): Promise<void> {
  // Process collections (status 300)
  await processBatch(
    false,
    config,
    startTime,
    endTime,
    batchSize,
    transactionHandler
  );

  // Process refunds (status 304)
  await processBatch(
    true,
    config,
    startTime,
    endTime,
    batchSize,
    transactionHandler
  );
}

async function processBatch(
  isRefund: boolean,
  config: ReconConfig,
  startTime: Date,
  endTime: Date,
  batchSize: number,
  transactionHandler: TransactionHandler
): Promise<void> {
  let offset = 0;
  let hasMore = true;

  while (hasMore) {
    let transactions: Transaction[] = [];

    try {
      transactions = await Transaction.findAll({
        where: {
          client_id: config.client_id,
          transaction_type: "Collection",
          ...(config.account_number && {
            account_number: config.account_number,
          }),
          status_code: isRefund ? 304 : 300,
          ...(isRefund
            ? {
                created_at: { [Op.lt]: startTime },
                updated_at: { [Op.between]: [startTime, endTime] },
              }
            : {
                created_at: { [Op.between]: [startTime, endTime] },
              }),
        },
        limit: batchSize,
        offset,
      });
    } catch (error) {
      throw new Error(
        `Failed to fetch ${
          isRefund ? "refund" : "collection"
        } batch at offset ${offset}: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }

    if (transactions.length === 0) {
      hasMore = false;
      break;
    }

    logger.info({
      message: `Processing ${
        isRefund ? "refund" : "collection"
      } batch | count ${
        transactions.length
      } | offset ${offset} | isRefund ${isRefund}`,
    });

    transactions.forEach((t) => transactionHandler(t, isRefund));
    offset += batchSize;
  }
}

import jwt from "jsonwebtoken";

class JWTHelper {
  sign({
    secret,
    payload,
    expiresIn,
  }: {
    secret: string;
    payload: any;
    expiresIn?: number;
  }) {
    return jwt.sign(payload, secret, { expiresIn });
  }

  verify({ token, secret }: { token: string; secret: string }) {
    try {
      return jwt.verify(token, secret);
    } catch (err) {
      return null;
    }
  }
}

export default JWTHelper;

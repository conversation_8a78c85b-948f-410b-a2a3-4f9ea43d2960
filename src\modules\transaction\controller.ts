import { Request, Response, NextFunction } from "express";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import ExcelJS from "exceljs";
import Logger from "../../helpers/logger";
import Transaction from "./model";
import { Op, Sequelize } from "sequelize";
import ClientUser from "../client_user/model";
import Service from "../service/model";
import Company from "../company/model";
import Client from "../client/model";
import { clientCallbackQueue } from "../../helpers/queues";
import StatusCode from "../status_code/model";
import {
  endOfToday,
  endOfYesterday,
  format,
  startOfToday,
  startOfYesterday,
  subHours,
} from "date-fns";
import { toZonedTime } from "date-fns-tz";
import redis from "../../config/redis";
import ms from "ms";
import ApiKey from "../api_key/model";
import axios from "axios";

const logger = new Logger();

class TransactionController {
  async getTransactions(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "getTransactions",
        message:
          "Received request to get transactions: REQUEST BODY: " +
          JSON.stringify(req.body),
      });

      let startOfDay = new Date();
      startOfDay.setHours(0, 0, 0, 0);

      let endofDay = new Date();
      endofDay.setHours(23, 59, 59, 999);

      let clients: any = [];

      if (req.user.company_id == 1) {
        const results = await Client.findAll();

        clients = results.map((result) => result.id);
      } else if (req.user.company_id != 1 && req.user.role == "ADMIN") {
        const results = await Client.findAll({
          where: { company_id: req.user.company_id },
        });

        clients = results.map((result) => result.id);
      } else {
        const results = await ClientUser.findAll({
          where: { user_id: req.user.id },
        });

        clients = results.map((result) => result.client_id);
      }

      logger.info({
        controller: "ReportsController",
        method: "accountsSummary",
        message: "ACCOUNTS: " + JSON.stringify(clients),
      });

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      const [transactions, transactionsSummary] = await Promise.all([
        Transaction.findAll({
          where: {
            created_at: {
              [Op.between]: [startOfDay, endofDay],
            },
            client_id: {
              [Op.in]: clients,
            },
          },
          offset,
          limit,
          order: [["created_at", "DESC"]],
          include: [
            { model: Client, as: "client", attributes: ["business_name"] },
          ],
        }),
        Transaction.findOne({
          attributes: [
            [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
            [Sequelize.fn("SUM", Sequelize.col("amount")), "totalAmount"],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (300, 240) THEN 1 ELSE 0 END"
                )
              ),
              "successfulCount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (300, 240) THEN amount ELSE 0 END"
                )
              ),
              "successfulAmount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (301, 239) THEN 1 ELSE 0 END"
                )
              ),
              "unsuccessfulCount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (301, 239) THEN amount ELSE 0 END"
                )
              ),
              "unsuccessfulAmount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (283, 238) THEN 1 ELSE 0 END"
                )
              ),
              "pendingCount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (283, 238) THEN amount ELSE 0 END"
                )
              ),
              "pendingAmount",
            ],
          ],
          where: {
            created_at: {
              [Op.between]: [startOfDay, endofDay],
            },
            client_id: {
              [Op.in]: clients,
            },
          },
        }),
      ]);

      const totalPages =
        Math.ceil(transactionsSummary?.dataValues.totalCount / limit) || 1;

      res.status(httpStatus.OK).json({
        data: transactions.map((txn: Transaction) => {
          return {
            ...txn.dataValues,
            updated_at: subHours(new Date(txn.dataValues.updated_at), 2),
          };
        }),
        summary: transactionsSummary,
        current_page: req.body.page,
        total_pages: totalPages,
        first_page: 1,
        next_page:
          req.body.page > totalPages
            ? null
            : req.body.page == totalPages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > totalPages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: totalPages,
      });
    } catch (err) {
      logger.error({
        controller: "TransactionController",
        method: "getTransactions",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getTransaction(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "getTransaction",
        message: "Received request to get transaction: ID ==> " + req.params.id,
      });

      const authUser = req.user;

      const transaction = await Transaction.findByPk(req.params.id);

      if (!transaction) {
        logger.info({
          controller: "TransactionController",
          method: "getTransaction",
          message: "Transaction not found",
        });

        return next(httpStatus.NOT_FOUND);
      }

      const isClientUser = await ClientUser.findOne({
        where: { client_id: transaction.client_id, user_id: authUser.id },
      });

      const isPrimenetUser = req.user.company_id == 1;

      const isClientCompanyAdmin =
        req.user.company_id == authUser.company_id && authUser.role == "ADMIN";

      if (!isClientUser && !isPrimenetUser && !isClientCompanyAdmin) {
        return res.sendStatus(httpStatus.FORBIDDEN);
      }

      var jsonTransaction = transaction.toJSON();

      var service = await Service.findByPk(transaction.service_id);
      var client = await Client.findByPk(transaction.client_id);

      jsonTransaction["service_name"] = service?.name;
      jsonTransaction["client_name"] = client?.business_name;
      jsonTransaction["updated_at"] = subHours(
        new Date(jsonTransaction["updated_at"]),
        2
      );

      res.status(httpStatus.OK).json(jsonTransaction);
    } catch (err) {
      logger.error({
        controller: "TransactionController",
        method: "getTransaction",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async reconcileTransaction(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "reconcileTransaction",
        message:
          "Received request to reconcile transaction: BODY ==> " +
          JSON.stringify(req.body),
      });

      const transaction = await Transaction.findOne({
        where: { external_id: req.body.external_id },
      });

      if (!transaction) {
        logger.info({
          controller: "TransactionController",
          method: "reconcileTransaction",
          message: "Transaction not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND));
      }

      var updatedTrsanction = await transaction.update({
        ...req.body,
        updated_by: req.user.id,
      });

      await clientCallbackQueue.add(updatedTrsanction);

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        controller: "TransactionController",
        method: "reconcileTransaction",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getTransactionsByFilter(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "getTransactionsByFilter",
        message:
          "Received get transactions by filter request: BODY ==> " +
          JSON.stringify(req.body),
      });

      const fromDate = req.body.from_date
        ? `${req.body.from_date} ${req.body.from_date_time}`
        : null;

      const toDate = req.body.to_date
        ? `${req.body.to_date} ${req.body.to_date_time}`
        : null;

      let clients: any = [];

      if (req.body.client_id) {
        const client = await Client.findByPk(req.body.client_id);

        if (!client) {
          logger.error({ message: "Client not found" });
          return next(
            createHttpError(httpStatus.NOT_FOUND, "Client not found")
          );
        }

        const isClientUser = await ClientUser.findOne({
          where: { client_id: req.body.client_id, user_id: req.user.id },
        });

        const isPrimenetUser = req.user.company_id == 1;

        const isClientCompanyAdmin =
          req.user.company_id == client.company_id && req.user.role == "ADMIN";

        if (!isClientUser && !isPrimenetUser && !isClientCompanyAdmin) {
          return res.sendStatus(httpStatus.FORBIDDEN);
        }

        clients = [req.body.client_id];
      } else {
        if (req.user.company_id == 1) {
          const results = await Client.findAll();

          clients = results.map((result) => result.id);
        } else if (req.user.company_id != 1 && req.user.role == "ADMIN") {
          const results = await Client.findAll({
            where: { company_id: req.user.company_id },
          });

          clients = results.map((result) => result.id);
        } else {
          const results = await ClientUser.findAll({
            where: { user_id: req.user.id },
          });

          clients = results.map((result) => result.client_id);
        }
      }

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      const [transactions, transactionsSummary] = await Promise.all([
        Transaction.findAll({
          where: {
            client_id: {
              [Op.in]: clients,
            },

            ...(fromDate &&
              toDate && {
                created_at: {
                  [Op.between]: [fromDate, toDate],
                },
              }),

            ...(req.body.status_code && { status_code: req.body.status_code }),
            ...(req.body.currency && { currency: req.body.currency }),

            ...(req.body.transaction_id && {
              transaction_id: req.body.transaction_id,
            }),

            ...(req.body.external_id && { external_id: req.body.external_id }),

            ...(req.body.transaction_type && {
              transaction_type: req.body.transaction_type,
            }),

            ...(req.body.service_id && {
              service_id: req.body.service_id,
            }),

            ...(req.body.phone_number && {
              phone_number: {
                [Op.like]: `%${req.body.phone_number}%`,
              },
            }),
            ...(req.body.account_number && {
              account_number: {
                [Op.like]: `%${req.body.account_number}%`,
              },
            }),
            ...(req.body.narration && {
              narration: {
                [Op.like]: `%${req.body.narration}%`,
              },
            }),
          },
          offset,
          limit,
          order: [["created_at", "DESC"]],
          include: [
            { model: Client, as: "client", attributes: ["business_name"] },
          ],
        }),
        Transaction.findOne({
          attributes: [
            [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
            [Sequelize.fn("SUM", Sequelize.col("amount")), "totalAmount"],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (300, 240) THEN 1 ELSE 0 END"
                )
              ),
              "successfulCount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (300, 240) THEN amount ELSE 0 END"
                )
              ),
              "successfulAmount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (301, 239) THEN 1 ELSE 0 END"
                )
              ),
              "unsuccessfulCount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (301, 239) THEN amount ELSE 0 END"
                )
              ),
              "unsuccessfulAmount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (283, 238) THEN 1 ELSE 0 END"
                )
              ),
              "pendingCount",
            ],
            [
              Sequelize.fn(
                "SUM",
                Sequelize.literal(
                  "CASE WHEN Transaction.status_code IN (283, 238) THEN amount ELSE 0 END"
                )
              ),
              "pendingAmount",
            ],
          ],
          where: {
            client_id: {
              [Op.in]: clients,
            },

            ...(fromDate &&
              toDate && {
                created_at: {
                  [Op.between]: [fromDate, toDate],
                },
              }),

            ...(req.body.status_code && { status_code: req.body.status_code }),
            ...(req.body.currency && { currency: req.body.currency }),

            ...(req.body.transaction_id && {
              transaction_id: req.body.transaction_id,
            }),

            ...(req.body.external_id && { external_id: req.body.external_id }),

            ...(req.body.transaction_type && {
              transaction_type: req.body.transaction_type,
            }),

            ...(req.body.service_id && { service_id: req.body.service_id }),

            ...(req.body.phone_number && {
              phone_number: {
                [Op.like]: `%${req.body.phone_number}%`,
              },
            }),
            ...(req.body.account_number && {
              account_number: {
                [Op.like]: `%${req.body.account_number}%`,
              },
            }),
            ...(req.body.narration && {
              narration: {
                [Op.like]: `%${req.body.narration}%`,
              },
            }),
          },
        }),
      ]);

      const totalPages =
        Math.ceil(transactionsSummary?.dataValues.totalCount / limit) || 1;

      res.status(httpStatus.OK).json({
        data: transactions.map((txn: Transaction) => {
          return {
            ...txn.dataValues,
            updated_at: subHours(new Date(txn.dataValues.updated_at), 2),
          };
        }),
        summary: transactionsSummary,
        current_page: req.body.page,
        total_pages: totalPages,
        first_page: 1,
        next_page:
          req.body.page > totalPages
            ? null
            : req.body.page == totalPages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > totalPages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: totalPages,
      });
    } catch (err) {
      logger.error({
        controller: "TransactionController",
        method: "getTransactionsByFilter",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getClientTransactions(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "getClientTransactions",
        message:
          "Received request to get client transactions: Client ==> " +
          req.params.id,
      });

      let startOfDay = new Date();
      startOfDay.setHours(0, 0, 0, 0);

      let endofDay = new Date();
      endofDay.setHours(23, 59, 59, 999);

      const transactions = await Transaction.findAll({
        where: {
          client_id: req.params.id,
          created_at: {
            [Op.between]: [startOfDay, endofDay],
          },
        },
        order: [["created_at", "DESC"]],
      });

      res.status(httpStatus.OK).json({
        data: transactions.map((txn: Transaction) => {
          return {
            ...txn.dataValues,
            updated_at: subHours(new Date(txn.dataValues.updated_at), 2),
          };
        }),
      });
    } catch (err) {
      logger.error({
        controller: "TransactionController",
        method: "getClientTransactions",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getAbsaTransactions(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "getAbsaTransactions",
        message: "Received request to get client transactions",
      });

      const today = new Date();
      const formattedDate = format(today, "yyyy-MM-dd");

      let fetchCountsKey = `${formattedDate}-absa-trxn-fetch-counts`;

      logger.info({
        controller: "TransactionController",
        method: "getAbsaTransactions",
        message: `Fetch counts key: ${fetchCountsKey}`,
      });

      let fetchCounts: number;

      let fetchCountsValue = await redis.get(fetchCountsKey);

      if (!fetchCountsValue) {
        await redis.set(fetchCountsKey, "0", "PX", ms("24h"));
        fetchCounts = 0;
      } else {
        fetchCounts = +fetchCountsValue;
      }

      logger.info({
        controller: "TransactionController",
        method: "getAbsaTransactions",
        message: `fetch counts: ${fetchCounts}`,
      });

      if (fetchCounts >= +process.env.MAX_ABSA_TRXN_FETCH_COUNTS) {
        return next(
          createHttpError(
            httpStatus.FORBIDDEN,
            "maximum daily fetch counts reached"
          )
        );
      }

      const clients = process.env.ABSA_ACCOUNTS.split(",").map(
        (client: string) => +client
      );

      const transactions = await Transaction.findAll({
        where: {
          client_id: {
            [Op.in]: clients,
          },
          status_code: {
            [Op.in]: [300, 304],
          },
          created_at: {
            [Op.between]: [startOfYesterday(), endOfYesterday()],
          },
        },
        attributes: [
          "amount",
          "currency",
          "transaction_id",
          "external_id",
          "phone_number",
          "account_number",
          "status_code",
          "narration",
          "created_at",
          "updated_at",
        ],
        order: [["created_at", "DESC"]],
      });

      await redis.incrby(fetchCountsKey, 1);

      res.status(httpStatus.OK).json({
        maxFetchCounts: +process.env.MAX_ABSA_TRXN_FETCH_COUNTS,
        remainingFetchCounts:
          +process.env.MAX_ABSA_TRXN_FETCH_COUNTS - (fetchCounts + 1),
        data: transactions.map((txn: Transaction) => {
          return {
            ...txn.dataValues,
            amount:
              txn.dataValues.status_code == 304
                ? -txn.dataValues.amount
                : +txn.dataValues.amount,
            updated_at: subHours(new Date(txn.dataValues.updated_at), 2),
          };
        }),
      });
    } catch (err) {
      logger.error({
        controller: "TransactionController",
        method: "getAbsaTransactions",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getClientTransactionsByFilter(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "getClientTransactionsByFilter",
        message:
          "Received get client transactions by filter request: BODY ==> " +
          JSON.stringify(req.body),
      });

      const fromDate = req.body.from_date
        ? `${req.body.from_date} ${req.body.from_date_time}`
        : null;

      const toDate = req.body.to_date
        ? `${req.body.to_date} ${req.body.to_date_time}`
        : null;

      const transactions = await Transaction.findAll({
        where: {
          client_id: req.params.id,

          ...(fromDate &&
            toDate && {
              created_at: {
                [Op.between]: [fromDate, toDate],
              },
            }),

          ...(req.body.status_code && { status_code: req.body.status_code }),
          ...(req.body.currency && { currency: req.body.currency }),

          ...(req.body.transaction_id && {
            transaction_id: req.body.transaction_id,
          }),

          ...(req.body.external_id && { external_id: req.body.external_id }),

          ...(req.body.transaction_type && {
            transaction_type: req.body.transaction_type,
          }),

          ...(req.body.service_id && {
            service_id: req.body.service_id,
          }),

          ...(req.body.phone_number && {
            phone_number: {
              [Op.like]: `%${req.body.phone_number}%`,
            },
          }),
          ...(req.body.account_number && {
            account_number: {
              [Op.like]: `%${req.body.account_number}%`,
            },
          }),
          ...(req.body.narration && {
            narration: {
              [Op.like]: `%${req.body.narration}%`,
            },
          }),
        },
        order: [["created_at", "DESC"]],
      });

      res.status(httpStatus.OK).json({
        data: transactions.map((txn: Transaction) => {
          return {
            ...txn.dataValues,
            updated_at: subHours(new Date(txn.dataValues.updated_at), 2),
          };
        }),
      });
    } catch (err) {
      logger.error({
        controller: "TransactionController",
        method: "getClientTransactionsByFilter",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async exportTransactions(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "exportTransactions",
        message:
          "Received export transactions request: BODY ==> " +
          JSON.stringify(req.body),
      });

      const fromDate = req.body.from_date
        ? `${req.body.from_date} ${req.body.from_date_time}`
        : startOfToday();

      const toDate = req.body.to_date
        ? `${req.body.to_date} ${req.body.to_date_time}`
        : endOfToday();

      let clients: any = [];

      if (req.body.client_id) {
        const client = await Client.findByPk(req.body.client_id);

        if (!client) {
          logger.error({ message: "Client not found" });
          return next(
            createHttpError(httpStatus.NOT_FOUND, "Client not found")
          );
        }

        const isClientUser = await ClientUser.findOne({
          where: { client_id: req.body.client_id, user_id: req.user.id },
        });

        const isPrimenetUser = req.user.company_id == 1;

        const isClientCompanyAdmin =
          req.user.company_id == client.company_id && req.user.role == "ADMIN";

        if (!isClientUser && !isPrimenetUser && !isClientCompanyAdmin) {
          return res.sendStatus(httpStatus.FORBIDDEN);
        }

        clients = [req.body.client_id];
      } else {
        if (req.user.company_id == 1) {
          const results = await Client.findAll();

          clients = results.map((result) => result.id);
        } else if (req.user.company_id != 1 && req.user.role == "ADMIN") {
          const results = await Client.findAll({
            where: { company_id: req.user.company_id },
          });

          clients = results.map((result) => result.id);
        } else {
          const results = await ClientUser.findAll({
            where: { user_id: req.user.id },
          });

          clients = results.map((result) => result.client_id);
        }
      }

      logger.info({
        controller: "TransactionController",
        method: "exportTransactions",
        message: "Fetching transactions",
      });

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=transactions.xlsx"
      );

      const workbook = new ExcelJS.stream.xlsx.WorkbookWriter({
        stream: res,
        useStyles: true,
        useSharedStrings: true,
      });

      const worksheet = workbook.addWorksheet("Transactions");

      worksheet.columns = [
        { header: "Company Name", key: "companyName", width: 20 },
        { header: "Business Name", key: "businessName", width: 20 },
        { header: "Transaction ID", key: "transactionId", width: 20 },
        { header: "Transaction Type", key: "transactionType", width: 20 },
        { header: "Amount", key: "amount", width: 15 },
        { header: "Phone Number", key: "phoneNumber", width: 15 },
        { header: "Account Number", key: "accountNumber", width: 20 },
        { header: "Narration", key: "narration", width: 30 },
        { header: "Service", key: "service", width: 20 },
        { header: "External ID", key: "externalId", width: 20 },
        { header: "Status", key: "status", width: 15 },
        { header: "Merchant ID", key: "merchantId", width: 20 },
        { header: "Currency", key: "currency", width: 10 },
        { header: "Response Message", key: "responseMessage", width: 30 },
        { header: "Response Code", key: "responseCode", width: 15 },
        { header: "Created At", key: "createdAt", width: 20 },
        { header: "Modified At", key: "modifiedAt", width: 20 },
      ];

      const limit = 50000;
      let offset = 0;
      let hasMore = true;
      let batchNumber = 1;

      const company = await Company.findByPk(req.user.company_id, {
        attributes: ["name"],
      });

      logger.info({
        controller: "TransactionController",
        method: "exportTransactions",
        message: "Fetching Transactions",
      });

      while (hasMore) {
        logger.info({
          controller: "TransactionController",
          method: "exportTransactions",
          message: "Transactions Batch NO: " + batchNumber,
        });

        const transactions = await Transaction.findAll({
          where: {
            client_id: {
              [Op.in]: clients,
            },

            ...(fromDate &&
              toDate && {
                created_at: {
                  [Op.between]: [fromDate, toDate],
                },
              }),

            ...(req.body.status_code && { status_code: req.body.status_code }),
            ...(req.body.currency && { currency: req.body.currency }),

            ...(req.body.transaction_id && {
              transaction_id: req.body.transaction_id,
            }),

            ...(req.body.external_id && { external_id: req.body.external_id }),

            ...(req.body.transaction_type && {
              transaction_type: req.body.transaction_type,
            }),

            ...(req.body.service_id && {
              service_id: req.body.service_id,
            }),

            ...(req.body.phone_number && {
              phone_number: {
                [Op.like]: `%${req.body.phone_number}%`,
              },
            }),
            ...(req.body.account_number && {
              account_number: {
                [Op.like]: `%${req.body.account_number}%`,
              },
            }),
            ...(req.body.narration && {
              narration: {
                [Op.like]: `%${req.body.narration}%`,
              },
            }),
          },
          limit,
          offset,
          order: [["created_at", "DESC"]],
          include: [
            { model: Client, as: "client", attributes: ["business_name"] },
            { model: Service, as: "service", attributes: ["name"] },
            {
              model: StatusCode,
              as: "statusCode",
              attributes: ["status_name"],
            },
          ],
        });

        if (transactions.length === 0) {
          hasMore = false;
          break;
        }

        transactions.forEach((item: any) => {
          worksheet
            .addRow([
              company?.dataValues.name,
              item.client.business_name,
              item.dataValues.transaction_id,
              item.dataValues.transaction_type,
              +item.dataValues.amount,
              +item.dataValues.phone_number,
              isNaN(item.dataValues.account_number)
                ? item.dataValues.account_number
                : +item.dataValues.account_number,
              item.dataValues.narration,
              item.service.name,
              item.dataValues.external_id,
              item.dataValues.statusCode.status_name,
              item.dataValues.merchant_id,
              item.dataValues.currency,
              item.dataValues.response_message,
              item.dataValues.response_code,
              format(
                toZonedTime(
                  new Date(item.dataValues.created_at),
                  "Africa/Lusaka"
                ),
                "dd/MM/yyyy, h:mm:ss a"
              ),
              format(
                toZonedTime(
                  subHours(new Date(item.dataValues.updated_at), 2),
                  "Africa/Lusaka"
                ),
                "dd/MM/yyyy, h:mm:ss a"
              ),
            ])
            .commit();
        });

        offset += limit;
        batchNumber += 1;
      }

      logger.info({
        controller: "TransactionController",
        method: "exportTransactions",
        message: "Completed Fetching Transactions",
      });

      worksheet.commit();
      await workbook.commit();
      res.end();
    } catch (err: any) {
      logger.error({
        controller: "TransactionController",
        method: "exportTransactions",
        message: "An error occurred: Err ==> " + err?.message,
      });
      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async refundTransaction(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "TransactionController",
        method: "refundTransaction",
        message:
          "Received request to refund transaction: BODY ==> " +
          JSON.stringify(req.body),
      });

      const transaction = await Transaction.findOne({
        where: { external_id: req.body.external_id },
      });

      if (!transaction) {
        logger.info({
          controller: "TransactionController",
          method: "refundTransaction",
          message: "Transaction not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND));
      }

      if (transaction.status_code != 300) {
        logger.info({
          controller: "TransactionController",
          method: "refundTransaction",
          message: "Transaction not eligible for refund",
        });

        return next(createHttpError(httpStatus.BAD_REQUEST));
      }

      const clientApi = await ApiKey.findOne({
        where: { client_id: transaction.client_id },
      });

      if (!clientApi) {
        logger.info({
          controller: "TransactionController",
          method: "refundTransaction",
          message: "API credentials not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND));
      }

      let url = process.env.PRIMENET_MOMO_REFUND_URL + req.body.external_id;
      let xAuth = clientApi.key;

      logger.info({
        controller: "TransactionController",
        method: "refundTransaction",
        message: `Refund URL -> ${url} | X-AUTHORIZATION: ${xAuth}`,
      });
      const response = await axios.post(
        url,
        {},
        {
          headers: {
            "X-AUTHORIZATION": xAuth,
          },
          responseType: "json",
          timeout: 60000,
        }
      );

      logger.info({
        controller: "TransactionController",
        method: "refundTransaction",
        message: `Refund API response: Status - ${
          response.status
        }, Data - ${JSON.stringify(response.data)}`,
      });

      if (response.status == httpStatus.ACCEPTED) {
        return res.sendStatus(httpStatus.OK);
      } else {
        return next(createHttpError(response.status, response.statusText));
      }
    } catch (err) {
      logger.error({
        controller: "TransactionController",
        method: "refundTransaction",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong -> " + JSON.stringify(err)
        )
      );
    }
  }
}

export default TransactionController;

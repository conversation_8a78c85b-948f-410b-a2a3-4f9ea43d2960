{"name": "next_payment_gateway_backend", "version": "1.0.0", "description": "Primenet Payment Gateway Backend", "main": "./dist/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node .", "dev": "tsnd --respawn ./src/server.ts", "build": "tsc"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/amqplib": "^0.10.5", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/http-errors": "^2.0.4", "@types/jsonwebtoken": "^9.0.6", "@types/ms": "^0.7.34", "@types/node": "^20.12.7", "@types/nodemailer": "^6.4.14", "@types/nodemailer-express-handlebars": "^4.0.5", "@types/numeral": "^2.0.5", "@types/otp-generator": "^4.0.2", "@types/uuid": "^10.0.0", "sequelize-cli": "^6.6.2", "ts-node-dev": "^2.0.0", "typescript": "^5.4.5"}, "dependencies": {"@faker-js/faker": "^9.3.0", "amqplib": "^0.10.4", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bull": "^4.12.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cron": "^4.1.3", "csv-stringify": "^6.5.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.19.2", "express-handlebars": "^7.1.2", "http-errors": "^2.0.0", "http-status": "^1.7.4", "joi": "^17.12.3", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.46", "ms": "^2.1.3", "mysql2": "^3.9.6", "nanoid": "^5.0.7", "nodemailer": "^6.9.13", "nodemailer-express-handlebars": "^6.1.2", "numeral": "^2.0.6", "otp-generator": "^4.0.1", "redis": "^5.6.0", "sequelize": "^6.37.3", "uuid": "^10.0.0", "winston": "^3.13.0"}, "engines": {"node": ">=20.13.0"}}
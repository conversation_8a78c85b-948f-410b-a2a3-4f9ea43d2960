import express from "express";
import ServiceController from "./controller";
import { superAdmin } from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();

const controller = new ServiceController();

router
  .route("/")
  .post(
    [superAdmin, validateReqData(validators.createService)],
    controller.createService
  )
  .get(superAdmin, controller.getServices);

router
  .route("/:id")
  .put(
    [superAdmin, validateReqData(validators.updateService)],
    controller.updateService
  )
  .delete(superAdmin, controller.deleteService);

export default router;

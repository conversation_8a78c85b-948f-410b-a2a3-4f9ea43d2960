import express from "express";
import { authenticate, companyAdmin } from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";
import ClientUserController from "./controller";

const router = express.Router();
const controller = new ClientUserController();

router
  .route("/")
  .post(
    [companyAdmin, validateReqData(validators.createClientUser)],
    controller.createClientUser
  )
  .delete(
    [companyAdmin, validateReqData(validators.deleteClientUser)],
    controller.deleteClientUser
  );

router.route("/:id").get(authenticate, controller.getClientUsers);

export default router;

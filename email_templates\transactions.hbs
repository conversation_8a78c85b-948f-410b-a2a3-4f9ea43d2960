<table>
  <tr class="bold">
    <td>TOTAL</td>
    <td>K{{defaultZero data.totalSummary.totalAmount}}</td>
    <td>{{defaultZero data.totalSummary.totalCount}}</td>
  </tr>
  <tr style="height: 24px;">
    <td colspan="3"></td>
  </tr>
  <tr class="bold">
    <td>COLLECTIONS</td>
    <td>K{{defaultZero data.collectionSummary.totalAmount}}</td>
    <td>{{defaultZero data.collectionSummary.totalCount}}</td>
  </tr>
  <tr>
    <td>Successful</td>
    <td>K{{defaultZero data.collectionSummary.successfulAmount}}</td>
    <td>{{defaultZero data.collectionSummary.successfulCount}}</td>
  </tr>
  <tr>
    <td>Failed</td>
    <td>K{{defaultZero data.collectionSummary.unsuccessfulAmount}}</td>
    <td>{{defaultZero data.collectionSummary.unsuccessfulCount}}</td>
  </tr>
  <tr class="bold">
    <td>Refund</td>
    <td>K{{defaultZero data.collectionSummary.refundAmount}}</td>
    <td>{{defaultZero data.collectionSummary.refundCount}}</td>
  </tr>
  {{#if data.refundsByMerchant}}
    {{#each data.refundsByMerchant}}
      <tr style="padding-left: 20px; font-size: 12px; color: #666;">
        <td style="padding-left: 20px;">• {{this.merchantName}}</td>
        <td>K{{defaultZero this.refundAmount}}</td>
        <td>{{defaultZero this.refundCount}}</td>
      </tr>
    {{/each}}
  {{/if}}
  <tr>
    <td>Ambigous</td>
    <td>K{{defaultZero data.collectionSummary.ambigousAmount}}</td>
    <td>{{defaultZero data.collectionSummary.ambigousCount}}</td>
  </tr>
  <tr style="height: 24px;">
    <td colspan="3"></td>
  </tr>
  <tr class="bold">
    <td>DISBURSEMENTS</td>
    <td>K{{defaultZero data.disbursementSummary.totalAmount}}</td>
    <td>{{defaultZero data.disbursementSummary.totalCount}}</td>
  </tr>
  <tr>
    <td>Successful</td>
    <td>K{{defaultZero data.disbursementSummary.successfulAmount}}</td>
    <td>{{defaultZero data.disbursementSummary.successfulCount}}</td>
  </tr>
  <tr>
    <td>Failed</td>
    <td>K{{defaultZero data.disbursementSummary.unsuccessfulAmount}}</td>
    <td>{{defaultZero data.disbursementSummary.unsuccessfulCount}}</td>
  </tr>
  {{#if data.disbursementsByMerchant}}
    {{#each data.disbursementsByMerchant}}
      <tr style="padding-left: 20px; font-size: 12px; color: #666;">
        <td style="padding-left: 20px;">• {{this.merchantName}}</td>
        <td>K{{defaultZero this.totalAmount}}</td>
        <td>{{defaultZero this.totalCount}}</td>
      </tr>
    {{/each}}
  {{/if}}
</table>
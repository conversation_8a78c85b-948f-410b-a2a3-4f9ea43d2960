import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";

class Company extends Model {
  declare id: any;
  declare email: string;
  declare name: string;
  declare address: string;
  declare phone_number: string;
  declare tpin: string;
  declare type_of_business: string;
  declare logo_url: string;
  declare description: string;
}

Company.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    phone_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    address: {
      type: DataTypes.CHAR,
    },
    type_of_business: {
      type: DataTypes.CHAR,
    },
    tpin: {
      type: DataTypes.CHAR,
    },
    logo_url: {
      type: DataTypes.CHAR,
    },
    description: {
      type: DataTypes.CHAR,
    },
  },
  {
    tableName: "companies",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

export default Company;

import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Client from "../client/model";

class Tariff extends Model {
  declare id: number;
  declare client_id: string;
  declare description: string;
  declare account_number: string;
  declare charge_type: string;
  declare match_token: string;
  declare token: string;
  declare fixed_amount: string;
  declare percent: string;
  declare ranges: string;
  declare created_at: Date;
  declare updated_at: Date;
}

Tariff.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    client_id: {
      type: DataTypes.BIGINT.UNSIGNED,
    },
    description: {
      type: DataTypes.STRING,
    },
    account_number: {
      type: DataTypes.STRING,
    },
    charge_type: {
      type: DataTypes.STRING,
    },
    match_token: {
      type: DataTypes.STRING,
    },
    token: {
      type: DataTypes.STRING,
    },
    fixed_amount: {
      type: DataTypes.STRING,
    },
    percent: {
      type: DataTypes.STRING,
    },
    ranges: {
      type: DataTypes.TEXT,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: "tariffs",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
    timestamps: true,
  }
);

Tariff.belongsTo(Client, {
  foreignKey: "client_id",
  as: "client",
  constraints: false,
});

export default Tariff;

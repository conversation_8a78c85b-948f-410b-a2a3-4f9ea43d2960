"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert("status_codes", [
      {
        status_code: "100",
        status_name: "Active Service",
        status_description: "service that can be used for transacting",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "101",
        status_name: "Deactivated service",
        status_description: "service that cannot be used for transacting",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "200",
        status_name: "Active User",
        status_description: "system active User",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "201",
        status_name: "Deactivated User",
        status_description: "deactivated system user",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "283",
        status_name: "Awaiting Payment Confirmation",
        status_description: "provided payload has missing parameters",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "278",
        status_name: "Pending Payment",
        status_description: "provided payload has missing parameters",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "284",
        status_name: "Invalid Payload",
        status_description: "provided payload has missing parameters",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "285",
        status_name: "Invalid Credentials",
        status_description: "invalid user credentials provided",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "289",
        status_name: "Transaction Failed",
        status_description: "An unknown error occurred",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "290",
        status_name: "Invalid Payer Number",
        status_description: "Number format received is invalid",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "300",
        status_name: "Successful Payment",
        status_description: "transaction successfully transacted",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "301",
        status_name: "Unsuccessful Payment",
        status_description: "transaction was not successfully transacted",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "302",
        status_name: "Pending Transaction",
        status_description: "transaction in progress",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "400",
        status_name: "Active Client",
        status_description: "client that can perform transactions",
        created_by: 1,
        created_at: new Date(),
      },
      {
        status_code: "401",
        status_name: "Deactivated Client",
        status_description: "client that cannot perform transactions",
        created_by: 1,
        created_at: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("status_codes", null, {});
  },
};

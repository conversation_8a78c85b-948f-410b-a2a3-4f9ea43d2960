import Joi from "joi";

const companyAccountsSummary = Joi.object({
  start_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/)
    .required(),
  end_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/)
    .required(),
  arrange_by_account_number: Joi.boolean().default(false),
  export: Joi.boolean().default(false),
  client_id: Joi.number().positive().allow(0),
  service_id: Joi.number().positive().allow(0),
  account_number: Joi.string(),
  limit: Joi.number().integer().default(15).max(20),
  page: Joi.number().integer().default(1),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const companiesSummary = Joi.object({
  start_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/)
    .required(),
  end_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/)
    .required(),
  export: Joi.boolean().default(false),
  company_id: Joi.number().positive().allow(0),
  service_id: Joi.number().positive().allow(0),
  limit: Joi.number().integer().default(15).max(20),
  page: Joi.number().integer().default(1),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default { companyAccountsSummary, companiesSummary };

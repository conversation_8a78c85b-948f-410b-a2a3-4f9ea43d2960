import { Sequelize } from "sequelize";
import dotenv from "dotenv";

dotenv.config();

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USERNAME,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: "mysql",
    timezone: "+02:00",
    logging: false,
  }
);

sequelize
  .sync({ force: false, alter: { drop: false } }) //! Set force to false to avoid dropping tables -> This should not be touched
  .then(() => console.log("Tables created successfully"))
  .catch((error) => console.error("Unable to create tables:", error));

export default sequelize;

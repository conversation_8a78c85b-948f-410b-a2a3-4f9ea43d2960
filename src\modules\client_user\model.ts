import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";

class ClientUser extends Model {
  declare id: any;
  declare user_id: any;
  declare client_id: number;
}

ClientUser.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    client_id: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
  },
  {
    tableName: "client_users",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

export default ClientUser;

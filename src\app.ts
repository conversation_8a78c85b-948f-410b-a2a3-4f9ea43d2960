import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import cookieParser from "cookie-parser";
import { errorHandler, notFound } from "./middlewares/errorMiddleware";

//TODO: Routes Imports

import authRoutes from "./modules/auth/routes";
import userRoutes from "./modules/user/routes";
import companyRoutes from "./modules/company/routes";
import clientRoutes from "./modules/client/routes";
import transactionRoutes from "./modules/transaction/routes";
import statsRoutes from "./modules/stats/routes";
import reportsRoutes from "./modules/reports/routes";
import serviceRoutes from "./modules/service/routes";
import clientUserRoutes from "./modules/client_user/routes";
import apiKeyRoutes from "./modules/api_key/routes";
import clientCallbackRoutes from "./modules/client_callback/routes";
import paymentLinksRoutes from "./modules/payment_links/routes";
import serviceAccountsRoutes from "./modules/service_accounts/routes";
import reconsRoutes from "./modules/recon/routes";

const app = express();

dotenv.config();

app.use(cors({ origin: process.env.CROSS_ORIGIN_URL, credentials: true }));
app.use(cookieParser());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// TODO:Routes
app.use("/auth", authRoutes);
app.use("/users", userRoutes);
app.use("/companies", companyRoutes);
app.use("/clients", clientRoutes);
app.use("/transactions", transactionRoutes);
app.use("/stats", statsRoutes);
app.use("/reports", reportsRoutes);
app.use("/services", serviceRoutes);
app.use("/client-users", clientUserRoutes);
app.use("/api-keys", apiKeyRoutes);
app.use("/client-callbacks", clientCallbackRoutes);
app.use("/payment-links", paymentLinksRoutes);
app.use("/service-accounts", serviceAccountsRoutes);
app.use("/recons", reconsRoutes);

app.get("/", (req, res) => {
  res.status(200).json({ msg: "Running..." });
});
//TODO: End of Routes

//TODO: Error handler for not found
app.use(notFound);

//TODO: Error handler for any other app errors
app.use(errorHandler);

export default app;

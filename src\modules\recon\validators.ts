import Joi from "joi";

const percentagePattern = /^\d{1,3}(\.\d+)?%$/;

const percentageValidator = Joi.string()
  .trim()
  .pattern(percentagePattern)
  .custom((value, helpers) => {
    const numericValue = parseFloat(value.replace("%", ""));
    if (numericValue < 0 || numericValue > 100) {
      return helpers.error("any.invalid");
    }
    return value;
  }, "Percentage Range Checker");

const createConfig = Joi.object({
  client_id: Joi.number().required(),
  account_number: Joi.string().trim(),
  account_name: Joi.string().trim(),
  momo_comm_rates: percentageValidator,
  card_comm_rates: percentageValidator,
  bank_commission: percentageValidator,
  revenue_share: Joi.string().valid("0", "1"),
  primenet_revenue_share: Joi.string().when("revenue_share", {
    is: "1",
    then: percentageValidator.required(),
    otherwise: Joi.optional,
  }),
  model: Joi.string(),
  email: Joi.string().email(),
}).options({ stripUnknown: true, abortEarly: false });

const updateConfig = Joi.object({
  client_id: Joi.string().trim().required(),
  account_number: Joi.string().trim(),
  account_name: Joi.string().trim(),
  momo_comm_rates: percentageValidator.allow(""),
  card_comm_rates: percentageValidator.allow(""),
  bank_commission: percentageValidator.allow(""),
  revenue_share: Joi.string().valid("0", "1"),
  primenet_revenue_share: Joi.string().when("revenue_share", {
    is: "1",
    then: percentageValidator.required(),
    otherwise: Joi.optional,
  }),
  model: Joi.string().allow(""),
  email: Joi.string().email().allow(""),
}).options({ stripUnknown: true, abortEarly: false });

const updateSettlementStatus = Joi.object({
  status: Joi.string().valid("PENDING", "SETTLED"),
}).options({ stripUnknown: true, abortEarly: false });

const getQueryStrings = Joi.object({
  limit: Joi.number().positive().default(20),
  page: Joi.number().positive().default(1),
  recon_type: Joi.string().valid("MOMO", "CARD"),
  currency: Joi.string().valid("ZMW", "USD"),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const getReconsByFilter = Joi.object({
  from_date: Joi.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  from_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("00:00"),
  to_date: Joi.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  to_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("23:59"),
  clients: Joi.array(),
  account_number: Joi.string().allow(""),
  status: Joi.string().allow(""),
  recon_type: Joi.string().valid("MOMO", "CARD"),
  currency: Joi.string().valid("ZMW", "USD"),
  limit: Joi.number().integer().min(1).max(20).default(15),
  page: Joi.number().integer().default(1),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const updateReconsStatus = Joi.object({
  from_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .allow(""),
  from_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("00:00")
    .allow(""),
  to_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .allow(""),
  to_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("23:59")
    .allow(""),
  clients: Joi.array(),
  account_number: Joi.string().allow(""),
  recon_type: Joi.string().valid("MOMO", "CARD"),
  currency: Joi.string().valid("ZMW", "USD"),
  status: Joi.string().allow(""),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const exportRecons = Joi.object({
  from_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .allow(""),
  from_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("00:00")
    .allow(""),
  to_date: Joi.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .allow(""),
  to_date_time: Joi.string()
    .regex(/^\d{2}:\d{2}$/)
    .default("23:59")
    .allow(""),
  clients: Joi.array(),
  account_number: Joi.string().allow(""),
  recon_type: Joi.string().valid("MOMO", "CARD"),
  currency: Joi.string().valid("ZMW", "USD"),
  status: Joi.string().allow(""),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default {
  createConfig,
  getQueryStrings,
  updateConfig,
  updateSettlementStatus,
  getReconsByFilter,
  exportRecons,
  updateReconsStatus,
};

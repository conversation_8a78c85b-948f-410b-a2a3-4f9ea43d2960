import Joi from "joi";

const createCompanyValidator = Joi.object({
  name: Joi.string().required(),
  email: Joi.string().email().required(),
  address: Joi.string().required(),
  phone_number: Joi.string().required(),
  tpin: Joi.string(),
  type_of_business: Joi.string(),
  logo_url: Joi.string(),
  description: Joi.string(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const updateCompanyValidator = Joi.object({
  name: Joi.string(),
  email: Joi.string().email(),
  address: Joi.string(),
  phone_number: Joi.string(),
  type_of_business: Joi.string(),
  logo_url: Joi.string(),
  description: Joi.string(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const getCompaniesValidator = Joi.object({
  limit: Joi.number().positive().default(20),
  page: Joi.number().positive().default(1),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const companySearchValidator = Joi.object({
  query: Joi.string().required(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default {
  createCompanyValidator,
  updateCompanyValidator,
  companySearchValidator,
  getCompaniesValidator,
};

import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Client from "../client/model";

class ClientCallback extends Model {
  declare id: any;
  declare service_id: number;
  declare client_callback_url: any;
  declare client_id: number;
  declare client: any;
  declare username: string;
  declare password: string;
}

ClientCallback.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    service_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    client_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    client_callback_url: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    username: {
      type: DataTypes.STRING,
    },
    password: {
      type: DataTypes.STRING,
    },
  },
  {
    tableName: "client_callback_information",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

ClientCallback.belongsTo(Client, { as: "client", foreignKey: "client_id" });

export default ClientCallback;

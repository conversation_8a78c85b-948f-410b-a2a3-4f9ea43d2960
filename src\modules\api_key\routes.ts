import express from "express";
import <PERSON>piKeyController from "./controller";
import { companyAdmin } from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();
const controller = new ApiKeyController();

router.post(
  "/",
  [companyAdmin, validateReqData(validators.createApiKey)],
  controller.createApiKey
);

router.get("/client/:id", companyAdmin, controller.getClientApiKeys);

router.delete("/:id", companyAdmin, controller.deleteApiKey);

export default router;

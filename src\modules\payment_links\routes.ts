import express from "express";
import { authenticate, clientUser } from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";
import PaymentLinksController from "./controller";

const router = express.Router();

const controller = new PaymentLinksController();

router
  .route("/")
  .post(
    [clientUser, validateReqData(validators.createPaymentLink)],
    controller.createPaymentLink
  );

router.get("/:id", controller.getPaymentLink);
router.post(
  "/share",
  [authenticate, validateReqData(validators.sharePaymentLink)],
  controller.sharePaymentLink
);

router.get("/client/:id", clientUser, controller.getPaymentLinks);

export default router;

"use strict";

const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const services = [];
    for (let i = 1; i <= 10; i++) {
      services.push({
        name: faker.commerce.productName(),
        min_amount: faker.finance.amount(10, 50, 2),
        max_amount: faker.finance.amount(100, 500, 2),
        service_type: faker.helpers.arrayElement([
          "PAYMENT",
          "TRANSFER",
          "TOPUP",
        ]),
        code: faker.string.alphanumeric(5),
        description: faker.lorem.sentence(),
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    return queryInterface.bulkInsert("services", services, {});
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete("services", null, {});
  },
};

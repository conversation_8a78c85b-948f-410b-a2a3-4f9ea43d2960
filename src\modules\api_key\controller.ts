/// <reference path="../../types/index.d.ts" />

import { NextFunction, Request, Response } from "express";
import Logger from "../../helpers/logger";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import { UniqueConstraintError, ValidationErrorItem } from "sequelize";
import Api<PERSON><PERSON> from "./model";
import HelperFunctions from "../../helpers";
import Client from "../client/model";

const logger = new Logger();

class ApiKeyController {
  async createApiKey(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ApiKeyController",
        method: "createApiKey",
        message:
          "Received create api key request: Body ==> " +
          JSON.stringify(req.body),
      });

      const authUser = req.user;

      const client = await Client.findByPk(req.body.client_id);

      if (!client) {
        logger.info({
          controller: "ApiKeyController",
          method: "createApiKey",
          message: "Client not found",
        });
        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      if (
        authUser.company_id != client.company_id &&
        authUser.role != "SUPERADMIN"
      ) {
        logger.info({
          controller: "ApiKeyController",
          method: "createApiKey",
          message: "Not a client admin or superadmin",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const apiKey = HelperFunctions.generateApiKey(client.id);

      await ApiKey.create({ ...req.body, key: apiKey });

      res.sendStatus(httpStatus.CREATED);
    } catch (err) {
      logger.error({
        controller: "ApiKeyController",
        method: "createApiKey",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      if (err instanceof UniqueConstraintError) {
        const _err = err as UniqueConstraintError;

        return res.status(httpStatus.CONFLICT).json(
          _err.errors.map((error: ValidationErrorItem) => {
            return {
              message: error.message,
              field: error.path,
              value: error.value,
            };
          })
        );
      }

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getClientApiKeys(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ApiKeyController",
        method: "getClientApiKeys",
        message:
          "Received get client api keys request: Client ==> " + req.params.id,
      });

      const authUser = req.user;

      const client = await Client.findByPk(req.params.id);

      if (!client) {
        logger.info({
          controller: "ApiKeyController",
          method: "getClientApiKeys",
          message: "Client not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      if (
        authUser.company_id != client.company_id &&
        authUser.role != "SUPERADMIN"
      ) {
        logger.info({
          controller: "ApiKeyController",
          method: "getClientApiKeys",
          message: "Not a client admin or superadmin",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const apiKeys = await ApiKey.findAll({
        where: { client_id: req.params.id },
      });

      res.status(httpStatus.OK).json({ data: apiKeys });
    } catch (err) {
      logger.error({
        controller: "ApiKeyController",
        method: "getClientApiKeys",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });
      return next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async deleteApiKey(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        message:
          "Received delete client api key request: Api Key Id ==> " +
          req.params.id,
        method: "deleteApiKey",
        controller: "ApiKeyController",
      });

      const authUser = req.user;

      const apiKey = await ApiKey.findByPk(req.params.id, {
        include: { model: Client, as: "client", attributes: ["company_id"] },
      });

      if (!apiKey) {
        logger.info({
          message: "Api key not found",
          method: "deleteApiKey",
          controller: "ApiKeyController",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Api Key not found"));
      }

      if (
        authUser.company_id != apiKey.client?.company_id &&
        authUser.role != "SUPERADMIN"
      ) {
        logger.info({
          message: "Not a client admin or superadmin",
          method: "deleteApiKey",
          controller: "ApiKeyController",
        });
        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      await apiKey.destroy();

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        message: "An error occured with: Err ==> " + JSON.stringify(err),
        method: "deleteApiKey",
        controller: "ApiKeyController",
      });

      return next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }
}

export default ApiKeyController;

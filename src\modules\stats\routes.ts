import express from "express";
import StatsController from "./controller";
import {
  clientUser,
  companyAdmin,
  primenetUser,
} from "../../middlewares/authMiddleware";

const router = express.Router();

const controller = new StatsController();

router.get("/daily", primenetUser, controller.getDailyStats);
router.get("/monthly", primenetUser, controller.getMonthlyStats);

router.get("/daily/company/:id", companyAdmin, controller.getDailyCompanyStats);

router.get(
  "/monthly/company/:id",
  companyAdmin,
  controller.getMonthlyCompanyStats
);

router.get("/daily/client/:id", clientUser, controller.getDailyClientStats);

router.get("/monthly/client/:id", clientUser, controller.getMonthlyClientStats);

export default router;

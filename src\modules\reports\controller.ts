import { NextFunction, Request, Response } from "express";
import { format } from "date-fns";
import Logger from "../../helpers/logger";
import ExcelJS from "exceljs";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Transaction from "../transaction/model";
import { Op, Sequelize } from "sequelize";
import Client from "../client/model";
import ClientUser from "../client_user/model";
import Company from "../company/model";

const logger = new Logger();

class ReportsController {
  async accountsSummary(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        message:
          "Received get company summaries for accounts transactions: BODY ==> " +
          JSON.stringify(req.body),
        controller: "ReportsController",
        method: "companyAccountsSummary",
      });

      const startDate = new Date(req.body.start_date);
      const endDate = new Date(req.body.end_date);

      if (req.body?.client_id) {
        const client = await Client.findByPk(req.body.client_id);

        if (!client) {
          logger.error({
            controller: "ReportsController",
            method: "accountsSummary",
            message: "Client not found",
          });

          return next(
            createHttpError(httpStatus.NOT_FOUND, "Client not found")
          );
        }

        const isClientUser = await ClientUser.findOne({
          where: { client_id: req.body.client_id, user_id: req.user.id },
        });

        const isPrimenetUser = req.user.company_id == 1;

        const isClientCompanyAdmin =
          req.user.company_id == client.company_id && req.user.role == "ADMIN";

        if (!isClientUser && !isPrimenetUser && !isClientCompanyAdmin) {
          logger.error({
            controller: "ReportsController",
            method: "accountsSummary",
            message: "User has no access to the account",
          });

          return next(createHttpError(httpStatus.FORBIDDEN));
        }
      }

      let clients: any = [];

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      let total_pages = 0;
      let total_results = 0;

      if (!req.body.client_id) {
        if (req.user.company_id == 1) {
          const { count: totalResults, rows: results } =
            await Client.findAndCountAll({
              ...(!req.body.export && { limit, offset }),
            });

          clients = results.map((result) => result.id);

          total_pages = Math.floor(totalResults / limit)
            ? Math.floor(totalResults / limit)
            : 1;
          total_results = totalResults;
        } else if (req.user.company_id != 1 && req.user.role == "ADMIN") {
          const { count: totalResults, rows: results } =
            await Client.findAndCountAll({
              ...(!req.body.export && { limit, offset }),
              where: { company_id: req.user.company_id },
            });

          clients = results.map((result) => result.id);

          total_pages = Math.floor(totalResults / limit)
            ? Math.floor(totalResults / limit)
            : 1;
          total_results = totalResults;
        } else {
          const { count: totalResults, rows: results } =
            await ClientUser.findAndCountAll({
              ...(!req.body.export && { limit, offset }),
              where: { user_id: req.user.id },
            });

          clients = results.map((result) => result.client_id);

          total_pages = Math.floor(totalResults / limit)
            ? Math.floor(totalResults / limit)
            : 1;

          total_results = totalResults;
        }

        logger.info({
          controller: "ReportsController",
          method: "accountsSummary",
          message: "ACCOUNTS: " + JSON.stringify(clients),
        });
      }

      const result = await Transaction.findAll({
        include: [
          { model: Client, as: "client", attributes: ["business_name"] },
        ],
        attributes: [
          "client_id",
          ...(req.body?.arrange_by_account_number || req.body?.account_number
            ? ["account_number"]
            : []),
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240,301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "totalCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240,301, 239) THEN amount ELSE 0 END"
              )
            ),
            "totalAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "successfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "successfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "unsuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "unsuccessfulAmount",
          ],
        ],
        group: [
          "client_id",
          ...(req.body?.arrange_by_account_number ? ["account_number"] : []),
        ],
        where: {
          created_at: {
            [Op.between]: [startDate, endDate],
          },
          ...(req.body?.client_id && { client_id: req.body.client_id }),
          ...(req.body?.service_id && { service_id: req.body.service_id }),
          ...(req.body?.account_number && {
            account_number: req.body.account_number,
          }),
          ...(!req.body?.client_id && {
            client_id: {
              [Op.in]: clients,
            },
          }),
        },
      });

      if (req.body.export) {
        if (req.body.arrange_by_account_number || req.body.account_number) {
          exportAccountsSummaryWithAcountNumber(result, req, res);
        } else {
          exportAccountsSummary(result, req, res);
        }
        return;
      }

      res.status(httpStatus.OK).json({
        summaries: result,
        total_results,
        current_page: req.body.page,
        total_pages: total_pages,
        first_page: 1,
        next_page:
          req.body.page > total_pages
            ? null
            : req.body.page == total_pages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > total_pages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: total_pages,
      });
    } catch (err) {
      logger.error({
        message: "Something went wrong",
        controller: "ReportsController",
        method: "companyAccountsSummary",
      });

      return next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async companiesSummary(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        message:
          "Received get company summaries : BODY ==> " +
          JSON.stringify(req.body),
        controller: "ReportsController",
        method: "companiesSummary",
      });

      const startDate = new Date(req.body.start_date);
      const endDate = new Date(req.body.end_date);

      let companies: any = [];

      const limit = req.body.limit;

      const offset = (req.body.page - 1) * limit;

      let total_pages = 0;
      let total_results = 0;

      if (!req.body.company_id) {
        const { count: totalResults, rows: results } =
          await Company.findAndCountAll({
            ...(!req.body.export && { limit, offset }),
          });

        companies = results.map((result) => result.id);

        total_pages = Math.floor(totalResults / limit)
          ? Math.floor(totalResults / limit)
          : 1;
        total_results = totalResults;

        logger.info({
          controller: "ReportsController",
          method: "companiesSummary",
          message: "companies: " + JSON.stringify(companies),
        });
      }

      const result = await Transaction.findAll({
        include: [
          {
            model: Client,
            as: "client",
            attributes: ["company_id"],
            include: [
              { model: Company, as: "company", attributes: ["id", "name"] },
            ],
            where: {
              ...(req.body?.company_id && {
                company_id: req.body.company_id,
              }),
              ...(!req.body?.company_id && {
                company_id: {
                  [Op.in]: companies,
                },
              }),
            },
          },
        ],
        attributes: [
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240,301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "totalCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240,301, 239) THEN amount ELSE 0 END"
              )
            ),
            "totalAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "successfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "successfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "unsuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "unsuccessfulAmount",
          ],
        ],
        group: ["client.company_id"],
        where: {
          created_at: {
            [Op.between]: [startDate, endDate],
          },
          ...(req.body?.service_id && { service_id: req.body.service_id }),
        },
      });

      if (req.body.export) {
        exportCompaniesSummary(result, req, res);
        return;
      }

      res.status(httpStatus.OK).json({
        summaries: result.map((rt: any) => ({
          totalCount: rt.dataValues.totalCount,
          totalAmount: rt.dataValues.totalAmount,
          successfulCount: rt.dataValues.successfulCount,
          successfulAmount: rt.dataValues.successfulAmount,
          unsuccessfulCount: rt.dataValues.unsuccessfulCount,
          unsuccessfulAmount: rt.dataValues.unsuccessfulAmount,
          company: rt.client.company,
        })),
        total_results,
        current_page: req.body.page,
        total_pages: total_pages,
        first_page: 1,
        next_page:
          req.body.page > total_pages
            ? null
            : req.body.page == total_pages
            ? null
            : req.body.page + 1,
        previous_page:
          req.body.page > total_pages
            ? null
            : req.body.page == 1
            ? null
            : req.body.page - 1,
        last_page: total_pages,
      });
    } catch (err) {
      logger.error({
        message: "Something went wrong",
        controller: "ReportsController",
        method: "companiesSummary",
      });

      return next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }
}

const exportCompaniesSummary = async (
  data: any,
  req: Request,
  res: Response
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Sheet 1", {
    properties: { defaultColWidth: 30 },
  });

  const formattedStartDate = format(
    new Date(req.body.start_date),
    "MM/dd/yyyy, hh:mm"
  );
  const formattedEndDate = format(
    new Date(req.body.end_date),
    "MM/dd/yyyy, hh:mm"
  );

  worksheet.addRow([`Companies Summary`]).font = {
    name: "Calibri",
    family: 2,
    size: 11,
    bold: true,
  };
  worksheet.addRow([]);
  worksheet.addRow([`Start date: ${formattedStartDate}`]);
  worksheet.addRow([`End date: ${formattedEndDate}`]);
  worksheet.addRow([]);
  worksheet.addRow([]);

  worksheet.addRow([
    "Company Name",
    "Successful Count",
    "Successful Amount",
    "Unsuccessful Count",
    "Unsuccessful Amount",
    "Total Count",
    "Total Amount",
  ]).font = {
    name: "Calibri",
    family: 2,
    size: 11,
    bold: true,
  };

  worksheet.getRow(7).height = 25;
  worksheet.getRow(7).alignment = {
    horizontal: "center",
    vertical: "middle",
  };

  // Add data rows
  data.forEach((item: any) => {
    worksheet.addRow([
      item.client.company.name,
      item.dataValues.successfulCount,
      item.dataValues.successfulAmount,
      item.dataValues.unsuccessfulCount,
      item.dataValues.unsuccessfulAmount,
      item.dataValues.totalCount,
      item.dataValues.totalAmount,
    ]).alignment = {
      horizontal: "center",
      vertical: "middle",
    };
  });

  // Save the workbook
  const buffer = await workbook.xlsx.writeBuffer();

  const fileName = `companies-summary`;

  res.setHeader("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
  res.send(buffer);
};

const exportAccountsSummary = async (
  data: any,
  req: Request,
  res: Response
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Sheet 1", {
    properties: { defaultColWidth: 30 },
  });

  const formattedStartDate = format(
    new Date(req.body.start_date),
    "MM/dd/yyyy, hh:mm"
  );
  const formattedEndDate = format(
    new Date(req.body.end_date),
    "MM/dd/yyyy, hh:mm"
  );

  worksheet.addRow([`Company Name: ${req.user.company.name}`]);
  worksheet.addRow([]);
  worksheet.addRow([`Start date: ${formattedStartDate}`]);
  worksheet.addRow([`End date: ${formattedEndDate}`]);
  worksheet.addRow([]);
  worksheet.addRow([]);

  worksheet.addRow([
    "Client Name",
    "Successful Count",
    "Successful Amount",
    "Unsuccessful Count",
    "Unsuccessful Amount",
    "Total Count",
    "Total Amount",
  ]).font = {
    name: "Calibri",
    family: 2,
    size: 11,
    bold: true,
  };

  worksheet.getRow(7).height = 25;
  worksheet.getRow(7).alignment = {
    horizontal: "center",
    vertical: "middle",
  };

  // Add data rows
  data.forEach((item: any) => {
    worksheet.addRow([
      item.client.business_name,
      item.dataValues.successfulCount,
      item.dataValues.successfulAmount,
      item.dataValues.unsuccessfulCount,
      item.dataValues.unsuccessfulAmount,
      item.dataValues.totalCount,
      item.dataValues.totalAmount,
    ]).alignment = {
      horizontal: "center",
      vertical: "middle",
    };
  });

  // Save the workbook
  const buffer = await workbook.xlsx.writeBuffer();

  const fileName = req.body.client_id ? `acccount-summary` : `accounts-summary`;

  res.setHeader("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
  res.send(buffer);
};

const exportAccountsSummaryWithAcountNumber = async (
  data: any,
  req: Request,
  res: Response
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Sheet 1", {
    properties: { defaultColWidth: 30 },
  });

  const formattedStartDate = format(
    new Date(req.body.start_date),
    "MM/dd/yyyy, hh:mm"
  );
  const formattedEndDate = format(
    new Date(req.body.end_date),
    "MM/dd/yyyy, hh:mm"
  );

  worksheet.addRow([`Company Name: ${req.user.company.name}`]);
  worksheet.addRow([]);
  worksheet.addRow([`Start date: ${formattedStartDate}`]);
  worksheet.addRow([`End date: ${formattedEndDate}`]);
  worksheet.addRow([]);
  worksheet.addRow([]);

  worksheet.addRow([
    "Client Name",
    "Merchant ID",
    "Successful Count",
    "Successful Amount",
    "Unsuccessful Count",
    "Unsuccessful Amount",
    "Total Count",
    "Total Amount",
  ]).font = {
    name: "Calibri",
    family: 2,
    size: 11,
    bold: true,
  };

  worksheet.getRow(7).height = 25;
  worksheet.getRow(7).alignment = {
    horizontal: "center",
    vertical: "middle",
  };

  // Add data rows
  data.forEach((item: any) => {
    worksheet.addRow([
      item.client.business_name,
      item.dataValues.account_number,
      item.dataValues.successfulCount,
      item.dataValues.successfulAmount,
      item.dataValues.unsuccessfulCount,
      item.dataValues.unsuccessfulAmount,
      item.dataValues.totalCount,
      item.dataValues.totalAmount,
    ]).alignment = {
      horizontal: "center",
      vertical: "middle",
    };
  });

  // Save the workbook
  const buffer = await workbook.xlsx.writeBuffer();

  const fileName = req.body.client_id ? `acccount-summary` : `accounts-summary`;

  res.setHeader("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
  res.send(buffer);
};

export default ReportsController;

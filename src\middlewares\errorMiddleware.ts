import httpStatus from "http-status";
import { Request, Response, NextFunction } from "express";

export const notFound = (req: Request, res: Response) => {
  res
    .status(httpStatus.NOT_FOUND)
    .json({ msg: `Resource not found - ${req.originalUrl}` });
};

export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  res.status(err.status ?? 500).json({ error: err.message });
};

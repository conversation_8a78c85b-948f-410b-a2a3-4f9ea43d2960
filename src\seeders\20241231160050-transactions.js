"use strict";

const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const clients = await queryInterface.sequelize.query(
      `SELECT id FROM clients`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (!clients.length) {
      throw new Error("No clients found. Seed the clients table first.");
    }

    const services = await queryInterface.sequelize.query(
      `SELECT id FROM services`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (!services.length) {
      throw new Error("No services found. Seed the services table first.");
    }

    const transactions = [];
    const totalRecords = 10000;

    for (let i = 0; i < totalRecords; i++) {
      const randomClient = clients[Math.floor(Math.random() * clients.length)];

      const randomService =
        services[Math.floor(Math.random() * services.length)];

      transactions.push({
        client_id: randomClient.id,
        service_id: randomService.id,
        amount: faker.finance.amount({
          autoFormat: false,
          min: 1,
          max: 50000,
          dec: 2,
        }),
        currency: faker.helpers.arrayElement(["ZMW", "USD"]),
        transaction_type: faker.helpers.arrayElement([
          "Collection",
          "Disbursement",
        ]),
        transaction_id: faker.string.uuid(),
        external_id: faker.string.uuid(),
        phone_number: faker.phone.number("+2609########"),
        account_number: faker.phone.number("+2609########"),
        response_message: faker.lorem.sentence({ max: 12 }),
        response_code: faker.helpers.arrayElement(["00", "01", "99"]),
        status_code: faker.helpers.arrayElement(["301", "300", "283"]),
        narration: faker.lorem.words(5),
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    await queryInterface.bulkInsert("transactions", transactions, {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("transactions", {}, {});
  },
};

import Joi from "joi";

const createPaymentLink = Joi.object({
  client_id: Joi.number().required(),
  amount: Joi.number().required(),
  currency: Joi.string().valid("ZMW").default("ZMW"),
  description: Joi.string().trim().allow(""),
  narration: Joi.string().max(50).trim(),
  expirySeconds: Joi.number()
    .integer()
    .min(60)
    .max(86400)
    .message("Expiry time must be >=60 and <=86400 seconds"),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const sharePaymentLink = Joi.object({
  plink_id: Joi.string().required(),
  purpose: Joi.string().required(),
  channel: Joi.string().valid("SMS", "EMAIL").required(),
  receiver_email: Joi.string()
    .email()
    .when("channel", {
      is: "EMAIL",
      then: Joi.required(),
    })
    .allow(""),
  receiver_number: Joi.string()
    .pattern(/^(26077|26097|26075|26095|26076|26096|26098)\d{7}$/)
    .when("channel", {
      is: "SMS",
      then: Joi.required(),
    })
    .allow(""),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default { createPaymentLink, sharePaymentLink };

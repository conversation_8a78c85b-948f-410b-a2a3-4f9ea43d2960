import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";

class StatusCode extends Model {
  declare id: any;
  declare status_name: string;
  declare status_code: string;
  declare status_description: string;
  declare created_by: string;
  declare created_at: any;
  declare updated_at: any;
}

StatusCode.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    status_name: {
      type: DataTypes.CHAR,
    },
    status_description: {
      type: DataTypes.CHAR,
    },
    created_by: {
      type: DataTypes.CHAR,
    },
    status_code: {
      type: DataTypes.CHAR,
    },
  },
  {
    tableName: "status_codes",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

export default StatusCode;

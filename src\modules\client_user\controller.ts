import { NextFunction, Request, Response } from "express";
import Logger from "../../helpers/logger";
import Client from "../client/model";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import User from "../user/model";
import ClientUser from "./model";
import { Op } from "sequelize";

const logger = new Logger();

class ClientUserController {
  async createClientUser(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientUserController",
        method: "createClientUser",
        message:
          "Received create client user: Body ==> " + JSON.stringify(req.body),
      });

      const authUser = req.user;

      const client_user = await ClientUser.findOne({ where: req.body });

      if (client_user) {
        logger.info({
          controller: "ClientUserController",
          method: "createClientUser",
          message: "User already a client user",
        });

        return next(
          createHttpError(httpStatus.CONFLICT, "User already a client user")
        );
      }

      const user = await User.findByPk(req.body.user_id);

      if (!user) {
        logger.info({
          controller: "ClientUserController",
          method: "createClientUser",
          message: "User not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "User not found"));
      }

      if (user.company_id == 1) {
        logger.info({
          controller: "ClientUserController",
          method: "createClientUser",
          message: "PrimeNet Users cannot be Assigned Accounts",
        });

        return next(
          createHttpError(
            httpStatus.FORBIDDEN,
            "PrimeNet Users cannot be Assigned Accounts"
          )
        );
      }

      const client = await Client.findByPk(req.body.client_id);

      if (!client) {
        logger.info({
          controller: "ClientUserController",
          method: "createClientUser",
          message: "Client not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      if (
        client.company_id != authUser.company_id &&
        authUser.role != "SUPERADMIN"
      ) {
        logger.info({
          controller: "ClientUserController",
          method: "createClientUser",
          message: "Client does not belong to superadmin company",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      await ClientUser.create(req.body);

      logger.info({
        controller: "ClientUserController",
        method: "createClientUser",
        message: "Client user created",
      });

      res.status(httpStatus.CREATED).json({ msg: "Client user created" });
    } catch (err) {
      logger.error({
        controller: "ClientUserController",
        method: "createClientUser",
        message: "An error occurred with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getClientUsers(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientUserController",
        method: "getClientUsers",
        message:
          "Received get client users request: Client ==> " + req.params.id,
      });

      const client = await Client.findByPk(req.params.id);

      if (!client) {
        logger.info({
          controller: "ClientUserController",
          method: "getClientUsers",
          message: "Client not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      const clientUsersRows = await ClientUser.findAll({
        where: { client_id: req.params.id },
      });

      const clientUsersIds = clientUsersRows.map(
        (clientUsersRow) => clientUsersRow.user_id
      );

      const clientUsers = await User.findAll({
        where: {
          id: {
            [Op.in]: clientUsersIds,
          },
        },
        attributes: ["id", "name", "email"],
      });

      res.status(httpStatus.OK).json({ data: clientUsers });
    } catch (err) {
      logger.error({
        controller: "ClientUserController",
        method: "getClientUsers",
        message: "An error occurred with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async deleteClientUser(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientUserController",
        method: "deleteClientUser",
        message:
          "Received delete client user request: Body ==> " +
          JSON.stringify(req.body),
      });

      const clientUser = await ClientUser.findOne({
        where: { client_id: req.body.client_id, user_id: req.body.user_id },
      });

      if (!clientUser) {
        logger.info({
          controller: "ClientUserController",
          method: "deleteClientUser",
          message: "Client user not found",
        });

        return next(
          createHttpError(httpStatus.NOT_FOUND, "Client user not found")
        );
      }

      await clientUser.destroy();

      res.status(httpStatus.OK).json({ msg: "Client user revoked" });
    } catch (err) {
      logger.error({
        controller: "ClientUserController",
        method: "deleteClientUser",
        message: "An error occurred with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }
}

export default ClientUserController;

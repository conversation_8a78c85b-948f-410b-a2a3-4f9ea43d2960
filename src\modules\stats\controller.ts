/// <reference path="../../types/index.d.ts" />

import { NextFunction, Request, Response } from "express";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Logger from "../../helpers/logger";
import Transaction from "../transaction/model";
import { Op, Sequelize } from "sequelize";
import Client from "../client/model";
import HelperFunctions from "../../helpers";
import Company from "../company/model";

const logger = new Logger();

class StatsController {
  async getDailyStats(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "StatsController",
        method: "getDailyPrimeNetStats",
        message: "Received get daily PrimeNet stats",
      });

      const startOfDay = new Date();
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date();
      endOfDay.setHours(23, 59, 59, 999);

      // Using aggregation to get the counts and sums
      const transactions = await Transaction.findAll({
        attributes: [
          [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
          [Sequelize.fn("SUM", Sequelize.col("amount")), "totalAmount"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "successfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "successfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "unsuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "unsuccessfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (283, 238) THEN 1 ELSE 0 END"
              )
            ),
            "pendingCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (283, 238) THEN amount ELSE 0 END"
              )
            ),
            "pendingAmount",
          ],
        ],
        where: {
          created_at: {
            [Op.between]: [startOfDay, endOfDay],
          },
        },
      });

      const result = transactions[0].get();

      // Assuming HelperFunctions.numberFormat is a utility function you have defined elsewhere
      const formattedTransactionsAmount = HelperFunctions.numberFormat(
        result.totalAmount
      );
      const formattedSuccessfulTransactionsAmount =
        HelperFunctions.numberFormat(result.successfulAmount);
      const formattedUnsuccessfulTransactionsAmount =
        HelperFunctions.numberFormat(result.unsuccessfulAmount);
      const formattedPendingTransactionsAmount = HelperFunctions.numberFormat(
        result.pendingAmount
      );

      res.status(httpStatus.OK).json({
        transactionsCount: result.totalCount,
        transactionsAmount: formattedTransactionsAmount,
        successfulTransactionsCount: result.successfulCount,
        successfulTransactionsAmount: formattedSuccessfulTransactionsAmount,
        unsuccessfulTransactionsCount: result.unsuccessfulCount,
        unsuccessfulTransactionsAmount: formattedUnsuccessfulTransactionsAmount,
        pendingTransactionsCount: result.pendingCount,
        pendingTransactionsAmount: formattedPendingTransactionsAmount,
      });
    } catch (err) {
      logger.error({
        controller: "StatsController",
        method: "getDailyPrimeNetStats",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getMonthlyStats(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "StatsController",
        method: "getMonthlyPrimeNetStats",
        message: "Received get PrimeNet monthly request",
      });

      const year = new Date().getFullYear();
      const startDate = `${year}-01-01 00:00:00`;
      const endDate = `${year}-12-31 23:59:59`;

      // Using aggregations to get the counts and sum amounts
      const transactions = await Transaction.findAll({
        attributes: [
          [Sequelize.fn("MONTH", Sequelize.col("created_at")), "month"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "totalSuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "totalUnsuccessfulCount",
          ],
          [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
          [Sequelize.fn("SUM", Sequelize.col("amount")), "totalAmount"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "totalSuccessfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "totalUnsuccessfulAmount",
          ],
        ],
        where: {
          created_at: {
            [Op.between]: [startDate, endDate],
          },
          status_code: { [Op.in]: [301, 239, 300, 240] },
        },
        group: [Sequelize.fn("MONTH", Sequelize.col("created_at"))],
      });

      // Preparing the monthly data
      const monthlyData: Record<
        string,
        {
          totalCount: number;
          totalSuccessfulCount: number;
          totalUnsuccessfulCount: number;
          totalAmount: number;
          totalSuccessfulAmount: number;
          totalUnsuccessfulAmount: number;
        }
      > = {
        jan: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        feb: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        mar: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        apr: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        may: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        jun: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        jul: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        aug: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        sep: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        oct: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        nov: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        dec: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
      };

      transactions.forEach((transaction) => {
        const monthIndex = transaction.getDataValue("month") - 1;
        const monthName = new Date(0, monthIndex)
          .toLocaleString("default", { month: "short" })
          .toLowerCase();

        monthlyData[monthName] = {
          totalCount: transaction.getDataValue("totalCount"),
          totalSuccessfulCount: transaction.getDataValue(
            "totalSuccessfulCount"
          ),
          totalUnsuccessfulCount: transaction.getDataValue(
            "totalUnsuccessfulCount"
          ),
          totalAmount: transaction.getDataValue("totalAmount"),
          totalSuccessfulAmount: transaction.getDataValue(
            "totalSuccessfulAmount"
          ),
          totalUnsuccessfulAmount: transaction.getDataValue(
            "totalUnsuccessfulAmount"
          ),
        };
      });

      res.status(200).json(monthlyData);
    } catch (err) {
      logger.error({
        controller: "StatsController",
        method: "getMonthlyPrimeNetStats",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getDailyClientStats(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "StatsController",
        method: "getDailyClientStats",
        message: "Received get daily client stats",
      });

      const clientId = parseInt(req.params.id, 10);

      let startOfDay = new Date();
      startOfDay.setHours(0, 0, 0, 0);

      let endofDay = new Date();
      endofDay.setHours(23, 59, 59, 999);

      const transactions = await Transaction.findAll({
        attributes: [
          [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
          [Sequelize.fn("SUM", Sequelize.col("amount")), "totalAmount"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "successfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "successfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "unsuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "unsuccessfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (283, 238) THEN 1 ELSE 0 END"
              )
            ),
            "pendingCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (283, 238) THEN amount ELSE 0 END"
              )
            ),
            "pendingAmount",
          ],
        ],
        where: {
          client_id: clientId,
          created_at: {
            [Op.between]: [startOfDay, endofDay],
          },
        },
      });

      const result = transactions[0].get();

      res.status(httpStatus.OK).json({
        transactionsCount: result.totalCount,
        transactionsAmount: result.totalAmount,
        successfulTransactionsCount: result.successfulCount,
        successfulTransactionsAmount: result.successfulAmount,
        unsuccessfulTransactionsCount: result.unsuccessfulCount,
        unsuccessfulTransactionsAmount: result.unsuccessfulAmount,
        pendingTransactionsCount: result.pendingCount,
        pendingTransactionsAmount: result.pendingAmount,
      });
    } catch (err) {
      logger.error({
        controller: "StatsController",
        method: "getDailyClientStats",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getMonthlyClientStats(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "StatsController",
        method: "getMonthlyClientStats",
        message: "Received get monthly client stats",
      });

      const clientId = parseInt(req.params.id, 10);

      const year = new Date().getFullYear();
      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;

      // Using aggregations to get the counts and sum amounts
      const transactions = await Transaction.findAll({
        attributes: [
          [Sequelize.fn("MONTH", Sequelize.col("created_at")), "month"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "totalSuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "totalUnsuccessfulCount",
          ],
          [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
          [Sequelize.fn("SUM", Sequelize.col("amount")), "totalAmount"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "totalSuccessfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "totalUnsuccessfulAmount",
          ],
        ],
        where: {
          client_id: clientId,
          created_at: {
            [Op.between]: [startDate, endDate],
          },
          status_code: { [Op.in]: [301, 239, 300, 240] },
        },
        group: [Sequelize.fn("MONTH", Sequelize.col("created_at"))],
      });

      // Preparing the monthly data
      const monthlyData: Record<
        string,
        {
          totalCount: number;
          totalSuccessfulCount: number;
          totalUnsuccessfulCount: number;
          totalAmount: number;
          totalSuccessfulAmount: number;
          totalUnsuccessfulAmount: number;
        }
      > = {
        jan: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        feb: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        mar: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        apr: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        may: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        jun: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        jul: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        aug: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        sep: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        oct: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        nov: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        dec: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
      };

      transactions.forEach((transaction) => {
        const monthIndex = transaction.getDataValue("month") - 1;
        const monthName = new Date(0, monthIndex)
          .toLocaleString("default", { month: "short" })
          .toLowerCase();

        monthlyData[monthName] = {
          totalCount: transaction.getDataValue("totalCount"),
          totalSuccessfulCount: transaction.getDataValue(
            "totalSuccessfulCount"
          ),
          totalUnsuccessfulCount: transaction.getDataValue(
            "totalUnsuccessfulCount"
          ),
          totalAmount: transaction.getDataValue("totalAmount"),
          totalSuccessfulAmount: transaction.getDataValue(
            "totalSuccessfulAmount"
          ),
          totalUnsuccessfulAmount: transaction.getDataValue(
            "totalUnsuccessfulAmount"
          ),
        };
      });

      res.status(200).json(monthlyData);
    } catch (err) {
      logger.error({
        controller: "StatsController",
        method: "getMonthlyClientStats",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getDailyCompanyStats(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "StatsController",
        method: "getDailyCompanyStats",
        message: "Received get daily company stats",
      });

      const authUser = req.user;
      const company = await Company.findByPk(req.params.id);

      if (!company) {
        logger.info({
          controller: "StatsController",
          method: "getDailyCompanyStats",
          message: "Company not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Company not found"));
      }

      if (authUser.company_id != company.id && authUser.company_id != 1) {
        logger.info({
          controller: "StatsController",
          method: "getDailyCompanyStats",
          message:
            "Superadmin does not belong to the company they are requesting daily stats for",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const startOfDay = new Date();
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date();
      endOfDay.setHours(23, 59, 59, 999);

      const clients = await Client.findAll({
        where: { company_id: req.params.id },
      });

      const clientsIds = clients.map((client) => client.id);

      // Using aggregation to get the counts and sums
      const transactions = await Transaction.findAll({
        attributes: [
          [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
          [Sequelize.fn("SUM", Sequelize.col("amount")), "totalAmount"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "successfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "successfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "unsuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "unsuccessfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (283, 238) THEN 1 ELSE 0 END"
              )
            ),
            "pendingCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (283, 238) THEN amount ELSE 0 END"
              )
            ),
            "pendingAmount",
          ],
        ],
        where: {
          client_id: {
            [Op.in]: clientsIds,
          },
          created_at: {
            [Op.between]: [startOfDay, endOfDay],
          },
        },
      });

      const result = transactions[0].get();

      res.status(httpStatus.OK).json({
        transactionsCount: result.totalCount,
        transactionsAmount: result.totalAmount,
        successfulTransactionsCount: result.successfulCount,
        successfulTransactionsAmount: result.successfulAmount,
        unsuccessfulTransactionsCount: result.unsuccessfulCount,
        unsuccessfulTransactionsAmount: result.unsuccessfulAmount,
        pendingTransactionsCount: result.pendingCount,
        pendingTransactionsAmount: result.pendingAmount,
      });
    } catch (err) {
      logger.error({
        controller: "StatsController",
        method: "getDailyCompanyStats",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getMonthlyCompanyStats(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      logger.info({
        controller: "StatsController",
        method: "getMonthlyCompanyStats",
        message: "Received get company monthly request",
      });

      const authUser = req.user;
      const company = await Company.findByPk(req.params.id);

      if (!company) {
        logger.info({
          controller: "StatsController",
          method: "getMonthlyCompanyStats",
          message: "Company not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Company not found"));
      }

      if (authUser.company_id != company.id && authUser.company_id != 1) {
        logger.info({
          controller: "StatsController",
          method: "getMonthlyCompanyStats",
          message:
            "Superadmin does not belong to the company they are requesting daily stats for",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const clients = await Client.findAll({
        ...(authUser.company_id != 1 && {
          where: { company_id: req.params.id },
        }),
      });

      const clientsIds = clients.map((client) => client.id);

      logger.info({
        controller: "StatsController",
        method: "getMonthlyCompanyStats",
        message: "Company clients ==> " + JSON.stringify(clientsIds),
      });

      const year = new Date().getFullYear();
      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;

      // Using aggregations to get the counts and sum amounts
      const transactions = await Transaction.findAll({
        attributes: [
          [Sequelize.fn("MONTH", Sequelize.col("created_at")), "month"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "totalSuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "totalUnsuccessfulCount",
          ],
          [Sequelize.fn("COUNT", Sequelize.col("*")), "totalCount"],
          [Sequelize.fn("SUM", Sequelize.col("amount")), "totalAmount"],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "totalSuccessfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "totalUnsuccessfulAmount",
          ],
        ],
        where: {
          ...(clientsIds.length > 0 && {
            client_id: {
              [Op.in]: clientsIds,
            },
          }),
          created_at: {
            [Op.between]: [startDate, endDate],
          },
          status_code: { [Op.in]: [301, 239, 300, 240] },
        },
        group: [Sequelize.fn("MONTH", Sequelize.col("created_at"))],
      });

      // Preparing the monthly data
      const monthlyData: Record<
        string,
        {
          totalCount: number;
          totalSuccessfulCount: number;
          totalUnsuccessfulCount: number;
          totalAmount: number;
          totalSuccessfulAmount: number;
          totalUnsuccessfulAmount: number;
        }
      > = {
        jan: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        feb: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        mar: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        apr: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        may: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        jun: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        jul: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        aug: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        sep: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        oct: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        nov: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
        dec: {
          totalCount: 0,
          totalSuccessfulCount: 0,
          totalUnsuccessfulCount: 0,
          totalAmount: 0,
          totalSuccessfulAmount: 0,
          totalUnsuccessfulAmount: 0,
        },
      };

      transactions.forEach((transaction) => {
        const monthIndex = transaction.getDataValue("month") - 1;
        const monthName = new Date(0, monthIndex)
          .toLocaleString("default", { month: "short" })
          .toLowerCase();

        monthlyData[monthName] = {
          totalCount: transaction.getDataValue("totalCount"),
          totalSuccessfulCount: transaction.getDataValue(
            "totalSuccessfulCount"
          ),
          totalUnsuccessfulCount: transaction.getDataValue(
            "totalUnsuccessfulCount"
          ),
          totalAmount: transaction.getDataValue("totalAmount"),
          totalSuccessfulAmount: transaction.getDataValue(
            "totalSuccessfulAmount"
          ),
          totalUnsuccessfulAmount: transaction.getDataValue(
            "totalUnsuccessfulAmount"
          ),
        };
      });

      res.status(200).json(monthlyData);
    } catch (err) {
      logger.error({
        controller: "StatsController",
        method: "getMonthlyCompanyStats",
        message: "An error occurred: " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }
}

export default StatsController;

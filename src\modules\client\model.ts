import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Company from "../company/model";

class Client extends Model {
  declare id: any;
  declare business_name: string;
  declare email: string;
  declare phone_number: string;
  declare address: string;
  declare company_id: any;
  declare status_code: string;
  declare created_by: number;
  declare updated_by: number;
  declare client_ip: string;
  declare secret_key: string;
  declare momo_rdr_url: string;
  declare checkout_properties: string;
  declare checkout_services: string;
}

Client.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    business_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    phone_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    address: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    company_id: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    status_code: {
      type: DataTypes.STRING,
    },
    client_ip: {
      type: DataTypes.STRING,
    },
    secret_key: {
      type: DataTypes.STRING,
    },
    momo_rdr_url: {
      type: DataTypes.STRING,
    },
    checkout_properties: {
      type: DataTypes.JSON,
    },
    checkout_services: {
      type: DataTypes.STRING,
      defaultValue: "OFF",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
  },
  {
    tableName: "clients",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

Client.belongsTo(Company, { foreignKey: "company_id", as: "company" });

export default Client;

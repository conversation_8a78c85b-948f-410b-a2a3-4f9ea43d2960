import Queue from "bull";
import sendEmail from "./sendEmail";
import Logger from "./logger";
import ClientUser from "../modules/client_user/model";
import Transaction from "../modules/transaction/model";
import axios from "axios";
import ClientCallback from "../modules/client_callback/model";

const emailQueue = new Queue("emailQueue");
const clientUserQueue = new Queue("clientUserQueue");
export const clientCallbackQueue = new Queue("clientCallbackQueue");

const logger = new Logger();

emailQueue.process(async function (job, done) {
  try {
    logger.info({ message: "Running Email Job" });

    const data = job.data as EmailObject;

    logger.info({ message: "data ===> " + JSON.stringify(data) });

    await sendEmail({
      template: data.template,
      subject: data.subject,
      email: data.email,
      name: data.name,
      token: data?.token,
      referenceCode: data?.referenceCode,
      link: data?.link,
      company: data?.company,
      password: data?.password,
      purpose: data?.purpose,
    });

    done();

    logger.info({ message: "Email sent successfully" });
  } catch (err) {
    done(new Error("error sending email"));

    logger.error({
      message: "Error sending email: Err ===> " + JSON.stringify(err),
    });
  }
});

clientUserQueue.process(async function (job, done) {
  try {
    logger.info({ message: "Running Client User Job" });

    const data = job.data as ClientUserObject;

    await ClientUser.create({ ...data });

    done();
  } catch (err) {
    done(new Error("error sending email"));
    logger.error({
      message: "Error creating client user: Err ===> " + JSON.stringify(err),
    });
  }
});

clientCallbackQueue.process(async function (job, done) {
  try {
    logger.info({ message: "Running Client Callback Job" });

    const transaction = job.data as Transaction;

    const clientCallbackInfo = await ClientCallback.findOne({
      where: {
        service_id: transaction.service_id,
        client_id: transaction.client_id,
      },
    });

    if (!clientCallbackInfo) {
      return;
    }

    const body = {
      final_status: transaction.status_code,
      username: clientCallbackInfo.username,
      password: clientCallbackInfo.password,
      transaction_id: transaction.transaction_id,
      narration: transaction.narration,
      response_message: transaction.response_message,
      order_id: transaction.external_id,
      amount: transaction.amount,
      payer_number: transaction.phone_number,
      account_number: transaction.account_number,
      currency: transaction.currency,
    };

    await axios.post(clientCallbackInfo.client_callback_url, body, {
      timeout: 30000,
    });

    logger.info({ message: "CLient callback sent" });

    done();
  } catch (err) {
    done(new Error("Error sending client callback"));
    logger.error({
      message: "Error sending client callback: Err ===> " + JSON.stringify(err),
    });
  }
});

clientCallbackQueue.on("completed", (job) => job.remove());
clientCallbackQueue.on("failed", (job) => job.remove());

emailQueue.on("completed", (job) => job.remove());
emailQueue.on("failed", (job) => job.remove());

clientUserQueue.on("completed", (job) => job.remove());
clientUserQueue.on("failed", (job) => job.remove());

export default { emailQueue, clientUserQueue };

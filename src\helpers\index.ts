import crypto from "crypto";
import util from "util";
import Transaction from "../modules/transaction/model";

const randomBytesAsync = util.promisify(crypto.randomBytes);

class HelperFunctions {
  static hasTokenExpired(tokenDuration: any) {
    const currentTime = new Date();

    const tokenExpiry = new Date(tokenDuration);

    if (currentTime > tokenExpiry) {
      return true;
    } else {
      return false;
    }
  }

  static numberFormat(
    num: number,
    decimals: number = 2,
    decPoint: string = ".",
    thousandsPoint: string = ","
  ): string {
    if (isNaN(num)) {
      throw new TypeError("number is not valid");
    }

    if (!num) {
      return "0";
    }

    let formattedNumStr = num.toFixed(decimals);

    let formattedNum = parseFloat(formattedNumStr);

    formattedNumStr = formattedNum.toString().replace(".", decPoint);

    let splitNum = formattedNumStr.split(decPoint);

    splitNum[0] = splitNum[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsPoint);

    formattedNumStr = splitNum.join(decPoint);

    return formattedNumStr;
  }

  static getMonthEndDate(month: number, year: number) {
    if (month === 2) {
      // February - check for leap year
      const isLeapYear =
        (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
      return isLeapYear ? `${year}-02-29 23:59` : `${year}-02-28 23:59`;
    }

    // End dates for other months
    const endDates: any = {
      1: `${year}-01-31 23:59`,
      3: `${year}-03-31 23:59`,
      4: `${year}-04-30 23:59`,
      5: `${year}-05-31 23:59`,
      6: `${year}-06-30 23:59`,
      7: `${year}-07-31 23:59`,
      8: `${year}-08-31 23:59`,
      9: `${year}-09-30 23:59`,
      10: `${year}-10-31 23:59`,
      11: `${year}-11-30 23:59`,
      12: `${year}-12-31 23:59`,
    };

    return endDates[month];
  }

  static isNumeric(str: string) {
    return /^\d+$/.test(str);
  }

  static generateApiKey(clientId: string) {
    const timestamp = Date.now().toString(36);
    const randomComponent = crypto.randomBytes(8).toString("hex");
    const combined = `${clientId}-${timestamp}-${randomComponent}`;

    const hash = crypto.createHash("sha256").update(combined).digest("hex");
    return hash;
  }

  static async generateUniqueKey() {
    const randomPart = await randomBytesAsync(18);

    const combinedKeyParts = randomPart.toString("hex");

    const encodedKey = combinedKeyParts.replace(/\+/g, "-").replace(/\//g, "_");

    return encodedKey;
  }

  static getMNO(phoneNumber: string): MNO | null {
    var prefix = phoneNumber.slice(0, 5);

    var mno: MNO | null = null;

    switch (prefix) {
      case "26097":
        mno = "AIRTEL";
        break;
      case "26077":
        mno = "AIRTEL";
        break;
      case "26096":
        mno = "MTN";
        break;
      case "26076":
        mno = "MTN";
        break;
      case "26095":
        mno = "ZAMTEL";
        break;
      case "26075":
        mno = "ZAMTEL";
        break;
      case "26098":
        mno = "ZEDMOBILE";
        break;
      default:
        break;
    }

    return mno;
  }

  static getCommission(txn: Transaction) {
    let commission = 0;

    const mno = HelperFunctions.getMNO(txn.phone_number);

    if (mno === "AIRTEL") {
      commission = HelperFunctions.airtelTransactionCommission(txn.amount);
    } else if (mno === "MTN") {
      commission = HelperFunctions.mtnTransactionCommission(txn.amount);
    } else if (mno === "ZAMTEL") {
      commission = HelperFunctions.zamtelTransactionCommission(txn.amount);
    }
    return commission;
  }

  static airtelTransactionCommission(amount: number) {
    if (amount >= 0 && amount <= 149.99) {
      return 0.5;
    } else if (amount >= 150 && amount <= 499.99) {
      return 1;
    } else if (amount >= 500 && amount <= 999.99) {
      return 1.5;
    } else if (amount >= 1000 && amount <= 2999.99) {
      return 2.8;
    } else if (amount >= 3000 && amount <= 4999.99) {
      return 4;
    } else if (amount >= 5000) {
      return 5.5;
    } else {
      return 0;
    }
  }

  static mtnTransactionCommission(amount: number) {
    if (amount >= 0 && amount <= 150) {
      return 0.5;
    } else if (amount > 150 && amount <= 500) {
      return 1;
    } else if (amount > 500 && amount <= 1000) {
      return 1.5;
    } else if (amount > 1000 && amount <= 3000) {
      return 3;
    } else if (amount > 3000 && amount <= 5000) {
      return 4;
    } else if (amount > 5000 && amount < 10000) {
      return 5.5;
    } else if (amount >= 10000) {
      return 5.5;
    } else {
      return 0;
    }
  }

  static zamtelTransactionCommission(amount: number) {
    if (amount >= 0 && amount <= 149.99) {
      return 0.5;
    } else if (amount >= 150 && amount <= 499.99) {
      return 1;
    } else if (amount >= 500 && amount <= 999.99) {
      return 1.5;
    } else if (amount >= 1000 && amount <= 2999.99) {
      return 2.8;
    } else if (amount >= 3000 && amount <= 4999.99) {
      return 4;
    } else if (amount >= 5000) {
      return 5.5;
    } else {
      return 0;
    }
  }
}

export default HelperFunctions;

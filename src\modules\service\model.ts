import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";

class Service extends Model {
  declare id: any;
  declare name: string;
  declare service_type: string;
  declare min_amount: string;
  declare max_amount: string;
  declare code: string;
  declare description: string;
}

Service.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    min_amount: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    max_amount: {
      type: DataTypes.CHAR,
      allowNull: false,
    },
    service_type: {
      type: DataTypes.CHAR,
    },
    code: {
      type: DataTypes.CHAR,
    },
    description: {
      type: DataTypes.CHAR,
    },
  },
  {
    tableName: "services",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

export default Service;

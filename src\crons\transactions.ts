import { Op, Sequelize } from "sequelize";
import Transaction from "../modules/transaction/model";
import Client from "../modules/client/model";
import sendEmail from "../helpers/sendEmail";
import {
  startOfToday,
  format,
  endOfToday,
  subHours,
  startOfYesterday,
  endOfYesterday,
  startOfMonth,
} from "date-fns";
import { CronJob } from "cron";

const instance = process.env.NODE_APP_INSTANCE || 0;

if (instance == 0) {
  // RUNS EVERYDAY AT 12:05 PM

  CronJob.from({
    cronTime: "5 12 * * *",
    onTick: async () => {
      try {
        let startTime = startOfToday();

        const endTime = subHours(endOfToday(), 12);

        await CronFunctions.transactions(startTime, endTime);
      } catch (error) {
        console.log("Something went wrong");
      }
    },
    start: true,
    timeZone: "Africa/Lusaka",
  });

  // RUNS EVERYDAY AT 01:00 AM

  CronJob.from({
    cronTime: "0 1 * * *",
    onTick: async () => {
      try {
        let startTime = startOfYesterday();

        const endTime = endOfYesterday();

        await CronFunctions.transactions(startTime, endTime);
      } catch (error) {
        console.log("Something went wrong");
      }
    },
    start: true,
    timeZone: "Africa/Lusaka",
  });

  // RUNS EVERY MONDAY AT 08:00 AM

  CronJob.from({
    cronTime: "0 8 * * 1",
    onTick: async () => {
      try {
        let startTime = startOfMonth(new Date());

        const endTime = new Date(Date.now());

        await CronFunctions.transactions(startTime, endTime, true);
      } catch (error) {
        console.log("Something went wrong");
      }
    },
    start: true,
    timeZone: "Africa/Lusaka",
  });
}

class CronFunctions {
  static async transactions(
    startTime: Date,
    endTime: Date,
    mtd: boolean = false
  ) {
    const [
      totalSummary,
      collectionSummary,
      disbursementSummary,
      refundsByMerchant,
      disbursementsByMerchant,
    ] = await Promise.all([
      Transaction.findOne({
        attributes: [
          [
            Sequelize.literal(
              "SUM(CASE WHEN Transaction.status_code IN (300, 301, 240, 304, 303, 239) THEN 1 ELSE 0 END)"
            ),
            "totalCount",
          ],
          [
            Sequelize.literal(
              "SUM(CASE WHEN Transaction.status_code IN (300, 301, 240, 304, 303, 239) THEN amount ELSE 0 END)"
            ),
            "totalAmount",
          ],
        ],
        where: {
          created_at: {
            [Op.between]: [startTime, endTime],
          },
        },
      }),
      Transaction.findOne({
        attributes: [
          [
            Sequelize.literal(
              "SUM(CASE WHEN Transaction.status_code IN (300, 301, 304, 303) THEN 1 ELSE 0 END)"
            ),
            "totalCount",
          ],
          [
            Sequelize.literal(
              "SUM(CASE WHEN Transaction.status_code IN (300, 301, 304, 303) THEN amount ELSE 0 END)"
            ),
            "totalAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300) THEN 1 ELSE 0 END"
              )
            ),
            "successfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300) THEN amount ELSE 0 END"
              )
            ),
            "successfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (304) THEN 1 ELSE 0 END"
              )
            ),
            "refundCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (304) THEN amount ELSE 0 END"
              )
            ),
            "refundAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301) THEN 1 ELSE 0 END"
              )
            ),
            "unsuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301) THEN amount ELSE 0 END"
              )
            ),
            "unsuccessfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (303) THEN 1 ELSE 0 END"
              )
            ),
            "ambigousCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (303) THEN amount ELSE 0 END"
              )
            ),
            "ambigousAmount",
          ],
        ],
        where: {
          created_at: {
            [Op.between]: [startTime, endTime],
          },
          transaction_type: "Collection",
        },
      }),
      Transaction.findOne({
        attributes: [
          [
            Sequelize.literal(
              "SUM(CASE WHEN Transaction.status_code IN (300, 240, 301, 239) THEN 1 ELSE 0 END)"
            ),
            "totalCount",
          ],
          [
            Sequelize.literal(
              "SUM(CASE WHEN Transaction.status_code IN (300, 240, 301, 239) THEN amount ELSE 0 END)"
            ),
            "totalAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "successfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "successfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "unsuccessfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "unsuccessfulAmount",
          ],
        ],
        where: {
          created_at: {
            [Op.between]: [startTime, endTime],
          },
          transaction_type: "Disbursement",
        },
      }),
      // Get refund breakdown by merchant
      Transaction.findAll({
        attributes: [
          [Sequelize.col("client.business_name"), "merchantName"],
          [
            Sequelize.fn("COUNT", Sequelize.col("Transaction.id")),
            "refundCount",
          ],
          [
            Sequelize.fn("SUM", Sequelize.col("Transaction.amount")),
            "refundAmount",
          ],
        ],
        include: [
          {
            model: Client,
            as: "client",
            attributes: [],
            required: true,
          },
        ],
        where: {
          created_at: {
            [Op.between]: [startTime, endTime],
          },
          transaction_type: "Collection",
          status_code: 304, // Refund status code
        },
        group: ["client.id", "client.business_name"],
        order: [
          [Sequelize.fn("SUM", Sequelize.col("Transaction.amount")), "DESC"],
        ],
        raw: true,
      }),
      // Get disbursement breakdown by merchant
      Transaction.findAll({
        attributes: [
          [Sequelize.col("client.business_name"), "merchantName"],
          [
            Sequelize.fn("COUNT", Sequelize.col("Transaction.id")),
            "totalCount",
          ],
          [
            Sequelize.fn("SUM", Sequelize.col("Transaction.amount")),
            "totalAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240) THEN 1 ELSE 0 END"
              )
            ),
            "successfulCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (300, 240) THEN amount ELSE 0 END"
              )
            ),
            "successfulAmount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301, 239) THEN 1 ELSE 0 END"
              )
            ),
            "failedCount",
          ],
          [
            Sequelize.fn(
              "SUM",
              Sequelize.literal(
                "CASE WHEN Transaction.status_code IN (301, 239) THEN amount ELSE 0 END"
              )
            ),
            "failedAmount",
          ],
        ],
        include: [
          {
            model: Client,
            as: "client",
            attributes: [],
            required: true,
          },
        ],
        where: {
          created_at: {
            [Op.between]: [startTime, endTime],
          },
          transaction_type: "Disbursement",
        },
        group: ["client.id", "client.business_name"],
        order: [
          [Sequelize.fn("SUM", Sequelize.col("Transaction.amount")), "DESC"],
        ],
        raw: true,
      }),
    ]);

    const formattedStartDate = format(startTime, "dd/MM/yyyy HH:mm:ss");
    const formattedEndDate = format(endTime, "dd/MM/yyyy HH:mm:ss");

    // Log breakdown for debugging
    console.log(
      `Collection refunds by merchant (${formattedStartDate} to ${formattedEndDate}):`,
      refundsByMerchant?.length
        ? refundsByMerchant
        : "No collection refunds found"
    );
    console.log(
      `Disbursements by merchant (${formattedStartDate} to ${formattedEndDate}):`,
      disbursementsByMerchant?.length
        ? disbursementsByMerchant
        : "No disbursements found"
    );

    await sendEmail({
      name: "David",
      subject: mtd
        ? `PrimeNet Weekly MTD Transactions. From: ${formattedStartDate}, To: ${formattedEndDate}`
        : `PrimeNet Daily Transactions. From: ${formattedStartDate}, To: ${formattedEndDate}`,
      template: "transactions",
      // receivers: ["<EMAIL>"],
      receivers: ["<EMAIL>"],
      layout: "notifications",
      data: {
        collectionSummary: collectionSummary?.dataValues,
        disbursementSummary: disbursementSummary?.dataValues,
        totalSummary: totalSummary?.dataValues,
        refundsByMerchant: refundsByMerchant,
        disbursementsByMerchant: disbursementsByMerchant,
      },
    });
  }
}

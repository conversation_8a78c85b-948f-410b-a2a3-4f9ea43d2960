import Joi from "joi";

const updateUser = Joi.object({
  name: Jo<PERSON>.string(),
  email: Joi.string().email(),
  phone_number: Joi.string(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const updateProfile = Joi.object({
  name: Joi.string(),
  email: Joi.string().email(),
  current_password: Joi.string().required(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const updatePassword = Joi.object({
  current_password: Joi.string().required(),
  new_password: Joi.string().required(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const createUser = Joi.object({
  name: Joi.string().required(),
  email: Joi.string().email().required(),
  phone_number: Joi.string(),
  company_id: Joi.number().required(),
  role: Joi.string()
    .valid("ACCOUNTS", "ADMIN", "SUPERADMIN")
    .default("ACCOUNTS"),
  accounts: Joi.array(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const getUsers = Joi.object({
  limit: Joi.number().positive().max(50).default(20),
  page: Joi.number().positive().default(1),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const userSearchValidator = Joi.object({
  query: Joi.string().required(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default {
  createUser,
  updateUser,
  getUsers,
  updatePassword,
  updateProfile,
  userSearchValidator,
};

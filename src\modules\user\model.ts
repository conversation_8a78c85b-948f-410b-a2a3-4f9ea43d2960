import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Company from "../company/model";

class User extends Model {
  declare id: any;
  declare name: any;
  declare email: any;
  declare phone_number: any;
  declare password: any;
  declare company_id: any;
  declare company: any;
  declare client_id: any;
  declare token: any;
  declare token_status: any;
  declare token_duration: any;
  declare status_code: any;
  declare role: any;
  declare created_by: any;
  declare created_at: any;
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    phone_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    company_id: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    client_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      defaultValue: 1,
    },
    status_code: {
      type: DataTypes.CHAR,
      defaultValue: 200,
    },
    token: {
      type: DataTypes.CHAR,
    },
    token_duration: {
      type: DataTypes.DATE,
    },
    created_by: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
    },
    role: {
      type: DataTypes.CHAR,
      defaultValue: "ACCOUNTS",
    },
  },
  {
    tableName: "users",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

export class EmailChange extends Model {
  declare id: any;
  declare email: any;
  declare token: any;
}

EmailChange.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    token: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    tableName: "email_change_requests",
    sequelize,
    createdAt: "created_at",
    updatedAt: false,
  }
);

User.belongsTo(Company, { foreignKey: "company_id", as: "company" });

export default User;

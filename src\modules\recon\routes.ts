import express from "express";
import ReconController from "./controller";
import { superAdmin, reconTeam } from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();
const controller = new ReconController();

router
  .route("/configs")
  .get(
    [superAdmin, validateReqData(validators.getQueryStrings)],
    controller.getConfigs
  )
  .post(
    [superAdmin, validateReqData(validators.createConfig)],
    controller.createConfig
  );

router
  .route("/")
  .get(
    [reconTeam, validateReqData(validators.getQueryStrings)],
    controller.getRecons
  );

router.post(
  "/filter",
  [reconTeam, validateReqData(validators.getReconsByFilter)],
  controller.getReconsByFilter
);

router.patch(
  "/status",
  [reconTeam, validateReqData(validators.updateReconsStatus)],
  controller.updateReconsStatus
);

router.post(
  "/export",
  [reconTeam, validateReqData(validators.exportRecons)],
  controller.exportRecons
);

router
  .route("/:id")
  .get(reconTeam, controller.getRecon)
  .patch(reconTeam, controller.updateSettlementStatus);

router
  .route("/configs/:id")
  .get(reconTeam, controller.getConfig)
  .put(
    [superAdmin, validateReqData(validators.updateConfig)],
    controller.updateConfig
  );

export default router;

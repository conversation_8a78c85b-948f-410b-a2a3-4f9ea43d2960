import express from "express";
import ReportsController from "./controller";
import { authenticate, superAdmin } from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();

const controller = new ReportsController();

router.get(
  "/summary/accounts",
  [authenticate, validateReqData(validators.companyAccountsSummary)],
  controller.accountsSummary
);

router.get(
  "/summary/companies",
  [superAdmin, validateReqData(validators.companiesSummary)],
  controller.companiesSummary
);

export default router;

import express from "express";
import AuthController from "./controller";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();
const controller = new AuthController();

router.post(
  "/login",
  validateReqData(validators.loginValidator),
  controller.login
);

router.post(
  "/complete-2fa",
  validateReqData(validators.complete2faValidator),
  controller.complete2fa
);

router.post(
  "/forgot-password",
  validateReqData(validators.emailValidator),
  controller.forgotPassword
);

router.post(
  "/reset-password/:token",
  validateReqData(validators.resetPasswordValidator),
  controller.resetPassword
);

router.post("/check-user", controller.checkUser);

export default router;

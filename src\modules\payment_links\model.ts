import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";

class PaymentLink extends Model {
  declare id: any;
  declare description: string;
  declare unique_id: string;
  declare external_ref: string;
  declare amount: string;
  declare currency: string;
  declare short_url: string;
  declare original_url: string;
  declare expiry_date: string;
  declare client_id: any;
  declare created_by: string;
  declare updated_by: string;
  declare created_at: string;
  declare updated_at: string;
}

PaymentLink.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    description: {
      type: DataTypes.STRING,
    },
    unique_id: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    amount: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    short_url: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    original_url: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    external_ref: {
      type: DataTypes.STRING,
    },
    expiry_date: {
      type: DataTypes.DATE,
    },
    client_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.STRING,
    },
    updated_by: {
      type: DataTypes.STRING,
    },
  },
  {
    tableName: "payment_links",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

export default PaymentLink;

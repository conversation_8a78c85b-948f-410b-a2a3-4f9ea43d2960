import hbs from "nodemailer-express-handlebars";
import nodemailer from "nodemailer";
import path from "path";
import numeral from "numeral";

const transporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  secure: false,
  auth: {
    user: process.env.EMAIL_USERNAME,
    pass: process.env.EMAIL_PASSWORD,
  },
});

transporter.use(
  "compile",
  hbs({
    viewPath: path.join(__dirname, "../../email_templates"),
    viewEngine: {
      extname: ".hbs",
      layoutsDir: path.join(__dirname, "../../email_templates/layouts"),
      defaultLayout: "layout",
      helpers: {
        defaultZero: function (value) {
          return value !== undefined && value !== null ? value : 0;
        },
        toFixed: function (number, decimals) {
          return parseFloat(number).toFixed(decimals);
        },
      },
    },
    extName: ".hbs",
  })
);

export const notificationsTransporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  secure: false,
  auth: {
    user: process.env.EMAIL_USERNAME,
    pass: process.env.EMAIL_PASSWORD,
  },
});

notificationsTransporter.use(
  "compile",
  hbs({
    viewPath: path.join(__dirname, "../../email_templates"),
    viewEngine: {
      extname: ".hbs",
      layoutsDir: path.join(__dirname, "../../email_templates/layouts"),
      defaultLayout: "notification",
      helpers: {
        defaultZero: function (value) {
          if (value === undefined || value === null) {
            return 0;
          }
          const num = Number(value);
          if (typeof num === "number" && !isNaN(num)) {
            return numeral(Math.round(num * 100) / 100).format("0,0.[00]");
          } else {
            return 0;
          }
        },
      },
    },
    extName: ".hbs",
  })
);

export default transporter;

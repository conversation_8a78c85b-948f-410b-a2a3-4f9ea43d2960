import Queue from "bull";
import Logger from "../helpers/logger";
import { v6 as uuidv6 } from "uuid";
import axios from "axios";
import { AxiosError } from "axios";

export const smsQueue = new Queue("nextPaymentGatewaySmsQueue");

const logger = new Logger();

smsQueue.process(async function (job, done) {
  try {
    logger.info({ message: "Running SMS Job" });

    const data = job.data;

    logger.info({ message: "data ===> " + JSON.stringify(data) });

    var externalId = uuidv6();

    const payload = {
      sourceAddr: data.sourceAddress ?? "PrimePay",
      messages: [
        {
          message: data.message,
          receivers: data.receivers,
          externalRef: externalId,
        },
      ],
    };

    await axios.post(process.env.PRIMENET_SMS_URL, payload, {
      headers: {
        "X-Authorization": process.env.PRIMENET_SMS_API_KEY,
      },
    });

    done();

    logger.info({ message: "SMS sent successfully" });
  } catch (err) {
    done(new Error("error sending SMS"));

    if (err instanceof AxiosError) {
      var _err = err as AxiosError;
      logger.error({
        message:
          "Error sending sms: Err ===> " + JSON.stringify(_err.response?.data),
      });
      return;
    }

    logger.error({
      message: "Error sending sms: Err ===> " + JSON.stringify(err),
    });
  }
});

smsQueue.on("completed", (job) => job.remove());
smsQueue.on("failed", (job) => job.remove());

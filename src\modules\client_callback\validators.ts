import Joi from "joi";

const createClientCallback = Joi.object({
  service_id: Joi.string().required(),
  client_id: Joi.string().required(),
  callback_url: Joi.string()
    .pattern(
      new RegExp(
        "^(https?:\\/\\/)?" + // protocol
          "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name and extension
          "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR ip (v4) address
          "(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port and path
          "(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
          "(\\#[-a-z\\d_]*)?$",
        "i"
      )
    )
    .messages({
      "string.pattern.base": "The callback URL is not valid.",
      "any.required": "A callback URL is required.",
    }), // fragment locator.required(),
}).options({
  abortEarly: false,
  stripUnknown: true,
});

export default { createClientCallback };

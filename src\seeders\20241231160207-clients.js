"use strict";

const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const companies = await queryInterface.sequelize.query(
      `SELECT id FROM companies`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (!companies.length) {
      throw new Error("No companies found. Seed the companies table first.");
    }

    const clients = [];
    for (let i = 1; i <= 1000; i++) {
      const randomCompany =
        companies[Math.floor(Math.random() * companies.length)];
      clients.push({
        business_name: faker.company.name(),
        email: faker.internet.email(),
        phone_number: faker.phone.number("+2609########"),
        address: faker.location.streetAddress(),
        company_id: randomCompany.id,
        status_code: faker.helpers.arrayElement(["ACTIVE", "INACTIVE"]),
        client_ip: faker.internet.ip(),
        secret_key: faker.string.alphanumeric(32),
        momo_rdr_url: faker.internet.url(),
        checkout_properties: JSON.stringify({
          color: faker.color.rgb(),
          theme: faker.helpers.arrayElement(["light", "dark"]),
        }),
        checkout_services: faker.helpers.arrayElement(["ON", "OFF"]),
        created_by: faker.number.int({ min: 1, max: 10 }),
        updated_by: faker.number.int({ min: 1, max: 10 }),
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    return queryInterface.bulkInsert("clients", clients, {});
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete("clients", null, {});
  },
};

import { NextFunction, Request, Response } from "express";
import { addSeconds, isAfter } from "date-fns";
import Logger from "../../helpers/logger";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Api<PERSON>ey from "../api_key/model";
import HelperFunctions from "../../helpers";
import PaymentLink from "./model";
import queues from "../../helpers/queues";
import Client from "../client/model";
import { smsQueue } from "../../queues/smsQueue";

const logger = new Logger();

export default class PaymentLinksController {
  async createPaymentLink(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "PaymentLinksController",
        method: "createPaymentLink",
        message:
          "Received create payment link request: Body ==> " +
          JSON.stringify(req.body),
      });

      var apiKey = await ApiKey.findOne({
        where: { client_id: req.body.client_id },
      });

      var externalRef = await HelperFunctions.generateUniqueKey();

      const { nanoid } = await import("nanoid");

      var unique_id = nanoid(10);

      const currentDate = new Date();

      const expiryDate = addSeconds(currentDate, req.body.expirySeconds);

      var original_url = `https://checkout.primenetpay.com/payment-checkout/${
        apiKey?.key
      }/${req.body.amount}/${externalRef}/prcz-card/${
        req.body.currency
      }?narration=${req.body?.narration ?? "payment link"}`;
      var short_url = `${process.env.FRONT_END_URL}/payment-links/${unique_id}`;

      await PaymentLink.create({
        unique_id,
        description: req.body?.description,
        amount: req.body?.amount,
        currency: req.body?.currency,
        short_url,
        original_url,
        expiry_date: expiryDate,
        external_ref: externalRef,
        client_id: req.body.client_id,
        created_by: req?.user?.id,
        updated_by: req?.user?.id,
      });

      logger.info({
        controller: "PaymentLinksController",
        method: "createPaymentLink",
        message: "Payment link created | link ==> " + original_url,
      });

      res.status(httpStatus.CREATED).json({
        link: short_url,
      });
    } catch (err: any) {
      console.log(err);

      logger.error({
        controller: "PaymentLinksController",
        method: "createPaymentLink",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getPaymentLink(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "PaymentLinksController",
        method: "getPaymentLink",
        message:
          "Received get payment link request: UNIQUE ID ==> " + req.params.id,
      });

      var paymentLink = await PaymentLink.findOne({
        where: { unique_id: req.params.id },
      });

      if (!paymentLink) {
        logger.info({
          controller: "PaymentLinksController",
          method: "getPaymentLink",
          message: "PAYMENT LINK NOT FOUND",
        });

        return next(createHttpError(httpStatus.NOT_FOUND));
      }

      const currentDate = new Date();

      if (isAfter(currentDate, paymentLink.expiry_date)) {
        logger.info({
          controller: "PaymentLinksController",
          method: "getPaymentLink",
          message: "PAYMENT LINK HAS EXPIRED",
        });

        return next(createHttpError(httpStatus.NOT_FOUND));
      }

      const client = await Client.findByPk(paymentLink.client_id);

      res.status(httpStatus.OK).json({
        link: paymentLink.original_url,
        client,
      });
    } catch (err: any) {
      logger.error({
        controller: "PaymentLinksController",
        method: "getPaymentLink",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async getPaymentLinks(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "PaymentLinksController",
        method: "getPaymentLinks",
        message:
          "Received get payment link request: CLIENT ID ==> " + req.params.id,
      });

      var paymentLinks = await PaymentLink.findAll({
        where: { client_id: req.params.id },
        order: [["created_at", "DESC"]],
      });

      res.status(httpStatus.OK).json({
        data: paymentLinks,
      });
    } catch (err: any) {
      logger.error({
        controller: "PaymentLinksController",
        method: "getPaymentLink",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }

  async sharePaymentLink(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "PaymentLinksController",
        method: "sharePaymentLink",
        message:
          "Received Share payment link request: BODY ==> " +
          JSON.stringify(req.body),
      });

      var paymentLink = await PaymentLink.findOne({
        where: { unique_id: req.body.plink_id },
      });

      if (!paymentLink) {
        logger.info({
          controller: "PaymentLinksController",
          method: "sharePaymentLink",
          message: "PAYMENT LINK NOT FOUND",
        });

        return next(createHttpError(httpStatus.NOT_FOUND));
      }

      const currentDate = new Date();

      if (isAfter(currentDate, paymentLink.expiry_date)) {
        logger.info({
          controller: "PaymentLinksController",
          method: "sharePaymentLink",
          message: "PAYMENT LINK HAS EXPIRED",
        });

        return next(createHttpError(httpStatus.NOT_FOUND));
      }

      if (req.body.channel == "EMAIL") {
        await queues.emailQueue.add({
          template: "payment_link",
          subject: "Payment Link",
          email: req.body.receiver_email,
          link: paymentLink.short_url,
          purpose: req.body.purpose,
        });
      } else {
        await smsQueue.add({
          message: `Kindly pay for ${req.body.purpose} using this link:\n${paymentLink.short_url}. Kindly note that, the link is valid for 24hrs`,
          receivers: [req.body.receiver_number],
        });
      }

      res.sendStatus(httpStatus.OK);
    } catch (err: any) {
      logger.error({
        controller: "PaymentLinksController",
        method: "sharePaymentLink",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(createHttpError(httpStatus.INTERNAL_SERVER_ERROR));
    }
  }
}

import express from "express";
import UserController from "./controller";
import { authenticate, companyAdmin } from "../../middlewares/authMiddleware";
import validators from "./validators";
import validateReqData from "../../middlewares/validateReqData";

const router = express.Router();
const controller = new UserController();

router
  .route("/")
  .post(
    [companyAdmin, validateReqData(validators.createUser)],
    controller.createUser
  );

router.get(
  "/search",
  [companyAdmin, validateReqData(validators.userSearchValidator)],
  controller.searchForUser
);

router
  .route("/set-login-attempts")
  .post(
    controller.setLoginAttempts
  );

  router
  .route("/clear-login-attempts")
  .post(
    controller.clearLoginAttempts
  );

  router
  .route("/get-login-attempts")
  .post(
    controller.getLoginAttempts
  );
router
  .route("/company/:id")
  .get(
    [companyAdmin, validateReqData(validators.getUsers)],
    controller.getCompanyUsers
  );

router.get("/user-details/:id", companyAdmin, controller.getUserData);
router.post("/complete-email-change/:token", controller.completeEmailChange);

router
  .route("/profile")
  .get(authenticate, controller.getMyProfile)
  .patch(
    [authenticate, validateReqData(validators.updateProfile)],
    controller.updateProfile
  );

router.patch(
  "/update-password",
  [authenticate, validateReqData(validators.updatePassword)],
  controller.updatePassword
);

router
  .route("/:id")
  .patch(
    [companyAdmin, validateReqData(validators.updateUser)],
    controller.updateUser
  )
  .delete(companyAdmin, controller.deleteUser);

export default router;

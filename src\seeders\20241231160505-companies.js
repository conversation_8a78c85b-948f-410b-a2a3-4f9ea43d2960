"use strict";

const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const companies = [];
    for (let i = 1; i <= 50; i++) {
      companies.push({
        name: faker.company.name(),
        email: faker.internet.email(),
        phone_number: faker.phone.number("+2609########"),
        address: faker.location.streetAddress(),
        type_of_business: faker.commerce.department(),
        tpin: faker.number.int({ min: 10000000, max: 99999999 }),
        logo_url: faker.image.urlPlaceholder({ width: 200, height: 200 }),
        description: faker.company.catchPhrase(),
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    return queryInterface.bulkInsert("companies", companies, {});
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete("companies", null, {});
  },
};

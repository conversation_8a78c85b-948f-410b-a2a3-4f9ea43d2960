import express from "express";
import TransactionController from "./controller";
import {
  AbsaAuthentication,
  authenticate,
  clientUser,
  superAdmin,
} from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();
const controller = new TransactionController();

router.get(
  "/",
  [authenticate, validateReqData(validators.getTransactions)],
  controller.getTransactions
);

router.get("/absa", AbsaAuthentication, controller.getAbsaTransactions);

router.post(
  "/filter",
  [authenticate, validateReqData(validators.getTransactionsByFilter)],
  controller.getTransactionsByFilter
);

router.post(
  "/export",
  [authenticate, validateReqData(validators.exportTransactions)],
  controller.exportTransactions
);

router.post(
  "/reconcile",
  [superAdmin, validateReqData(validators.reconcileTransaction)],
  controller.reconcileTransaction
);

router.get("/:id", authenticate, controller.getTransaction);

router.get("/client/:id", clientUser, controller.getClientTransactions);

router.post(
  "/filter/client/:id",
  [clientUser, validateReqData(validators.getTransactionsByFilter)],
  controller.getClientTransactionsByFilter
);

router.post(
  "/refund",
  [superAdmin, validateReqData(validators.refundTransaction)],
  controller.refundTransaction
);

export default router;

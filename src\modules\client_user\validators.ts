import Joi from "joi";

const createClientUser = Joi.object({
  client_id: Joi.string().required(),
  user_id: Joi.string().required(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

const deleteClientUser = Joi.object({
  client_id: Joi.string().required(),
  user_id: Joi.string().required(),
}).options({
  stripUnknown: true,
  abortEarly: false,
});

export default { createClientUser, deleteClientUser };

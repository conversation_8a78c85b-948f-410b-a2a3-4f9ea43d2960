import express from "express";
import ClientController from "./controller";
import { superAdmin, primenetUser } from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();
const controller = new ClientController();

router
  .route("/")
  .get(
    [primenetUser, validateReqData(validators.getClients)],
    controller.getClients
  )
  .post(
    [superAdmin, validateReqData(validators.createClient)],
    controller.createClient
  );

router
  .route("/:id")
  .get(primenetUser, controller.getClient)
  .put(
    [superAdmin, validateReqData(validators.updateClient)],
    controller.updateClient
  )
  .delete(superAdmin, controller.deleteClient);

router.patch(
  "/:id/checkout-services",
  [superAdmin, validateReqData(validators.updateClientCheckoutServices)],
  controller.updateClientCheckoutServices
);

router.patch(
  "/:id/checkout-properties",
  [superAdmin, validateReqData(validators.updateClientCheckoutProperties)],
  controller.updateClientCheckoutProperties
);

export default router;

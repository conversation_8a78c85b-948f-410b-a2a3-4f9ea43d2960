import { NextFunction, Request, Response } from "express";
import createHttpError from "http-errors";
import httpStatus from "http-status";
import Logger from "../../helpers/logger";
import ClientCallback from "./model";
import Client from "../client/model";
import Service from "../service/model";

const logger = new Logger();

class ClientCallbackController {
  async createClientCallback(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientCallbackController",
        method: "createClientCallback",
        message:
          "Received create client callback request: Body ==> " +
          JSON.stringify(req.body),
      });

      const authUser = req.user;

      const client = await Client.findByPk(req.body.client_id);

      if (!client) {
        logger.info({
          controller: "ClientCallbackController",
          method: "createClientCallback",
          message: "Client not found",
        });

        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      if (
        authUser.company_id != client.company_id &&
        authUser.role != "SUPERADMIN"
      ) {
        logger.info({
          controller: "ClientCallbackController",
          method: "createClientCallback",
          message: "Not a client admin or superadmin",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const clientCallback = await ClientCallback.findOne({
        where: {
          service_id: req.body.service_id,
          client_id: req.body.client_id,
        },
      });

      if (clientCallback) {
        logger.info({
          controller: "ClientCallbackController",
          method: "createClientCallback",
          message: "Client callback for the selected service already exist",
        });

        return next(
          createHttpError(
            httpStatus.CONFLICT,
            "Client callback for the selected service already exist"
          )
        );
      }

      const service = await Service.findByPk(req.body.service_id);

      if (!service) {
        return next(createHttpError(httpStatus.NOT_FOUND, "Service not found"));
      }

      await ClientCallback.create({
        service_id: req.body.service_id,
        client_id: req.body.client_id,
        client_callback_url: req.body.callback_url,
      });

      res.sendStatus(httpStatus.CREATED);
    } catch (err) {
      logger.error({
        controller: "ClientCallbackController",
        method: "createClientCallback",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async getClientCallbacks(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientCallbackController",
        method: "getClientCallbacks",
        message: "Received get client callbacks",
      });

      const authUser = req.user;

      const client = await Client.findByPk(req.params.id);

      if (!client) {
        logger.info({
          controller: "ClientCallbackController",
          method: "getClientCallbacks",
          message: "Client not found",
        });
        return next(createHttpError(httpStatus.NOT_FOUND, "Client not found"));
      }

      if (
        authUser.company_id != client.company_id &&
        authUser.role != "SUPERADMIN"
      ) {
        logger.info({
          controller: "ClientCallbackController",
          method: "getClientCallbacks",
          message: "Not a client admin or superadmin",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      const clientCallbacks = await ClientCallback.findAll({
        where: {
          client_id: req.params.id,
        },
      });

      res.status(httpStatus.OK).json({ data: clientCallbacks });
    } catch (err) {
      logger.error({
        controller: "ClientCallbackController",
        method: "getClientCallbacks",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }

  async deleteClientCallback(req: Request, res: Response, next: NextFunction) {
    try {
      logger.info({
        controller: "ClientCallbackController",
        method: "deleteClientCallback",
        message:
          "Received delete client callback: Callback ==> " + req.params.id,
      });

      const authUser = req.user;

      const clientCallback = await ClientCallback.findByPk(req.params.id, {
        include: { model: Client, as: "client", attributes: ["company_id"] },
      });

      if (!clientCallback) {
        logger.info({
          controller: "ClientCallbackController",
          method: "deleteClientCallback",
          message: "Client callback not found",
        });

        return next(
          createHttpError(httpStatus.NOT_FOUND, "Client callback not found")
        );
      }

      if (
        authUser.company_id != clientCallback.client?.company_id &&
        authUser.role != "SUPERADMIN"
      ) {
        logger.info({
          controller: "ClientCallbackController",
          method: "deleteClientCallback",
          message: "Not a client admin or superadmin",
        });

        return next(createHttpError(httpStatus.FORBIDDEN));
      }

      await clientCallback.destroy();

      res.sendStatus(httpStatus.OK);
    } catch (err) {
      logger.error({
        controller: "ClientCallbackController",
        method: "deleteClientCallback",
        message: "An error occured with: Err ==> " + JSON.stringify(err),
      });

      next(
        createHttpError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong"
        )
      );
    }
  }
}

export default ClientCallbackController;

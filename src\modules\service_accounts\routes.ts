import express from "express";
import ServiceAccountsController from "./controller";
import {
  AbsaAuthentication,
  authenticate,
  clientUser,
  superAdmin,
} from "../../middlewares/authMiddleware";
import validateReqData from "../../middlewares/validateReqData";
import validators from "./validators";

const router = express.Router();
const controller = new ServiceAccountsController();


router.get("/float/:clientId", controller.getClientServiceAccounts);


export default router;

import sequelize from "../../config/db";
import { Model, DataTypes } from "sequelize";
import Client from "../client/model";
import Service from "../service/model";
import StatusCode from "../status_code/model";
import Accounts from "../accounts/model";

class ServiceAccounts extends Model {
  declare id: any;
  declare amount: any;
  declare client_id: any;
  declare service_id: any;
  declare created_by: any;
  declare updated_by: any;
  declare status_code: any;
  declare created_at: any;
  declare updated_at: any;
}

ServiceAccounts.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    client_id: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    service_id: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    amount: {
      type: DataTypes.STRING,
    },
    status_code: {
      type: DataTypes.STRING,
    },
    created_by: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
    updated_by: {
      type: DataTypes.INTEGER.UNSIGNED,
    },
  },
  {
    tableName: "service_accounts",
    sequelize,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
    ],
  }
);

ServiceAccounts.belongsTo(Client, {
  foreignKey: "client_id",
  as: "client",
  constraints: false,
});

ServiceAccounts.belongsTo(Service, { foreignKey: "service_id", as: "service" });

ServiceAccounts.belongsTo(StatusCode, {
  foreignKey: "status_code",
  targetKey: "status_code",
  as: "statusCode",
});

ServiceAccounts.belongsTo(Accounts, {
  foreignKey: "client_id",
  targetKey: "client_id",
  as: "account",
});

export default ServiceAccounts;
